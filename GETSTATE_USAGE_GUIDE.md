# GetState Usage Guide

This guide demonstrates how to use the `GetState` method from `<PERSON>Batch` in various scenarios within the SFC contract.

## Overview

The `GetState` method allows you to read state values that might have been modified within the same batch operation. This is crucial for:

1. **Read-after-write scenarios** within the same batch
2. **Conditional logic** based on current state (including pending changes)
3. **Complex state transitions** that depend on multiple related values
4. **Getter functions** that need to return the most current state

## Basic Usage Pattern

```go
func exampleFunction(evm *vm.EVM, validatorID *big.Int) (uint64, error) {
    batch := NewStateBatch()
    defer batch.Clear()
    
    var gasUsed uint64 = 0
    
    // Calculate slot
    statusSlot, slotGas := getValidatorStatusSlot(validatorID)
    gasUsed += slotGas
    
    // Read current value (includes pending writes)
    currentStatus := batch.GetState(evm, common.BigToHash(statusSlot))
    
    // Use the value for conditional logic
    if currentStatus.Big().Cmp(big.NewInt(0)) == 0 {
        // Validator is active, perform some action
        newValue := big.NewInt(1)
        batch.SetState(common.BigToHash(statusSlot), common.BigToHash(newValue))
    }
    
    // Execute all batched updates
    batchGasUsed := batch.BatchWrite(evm)
    gasUsed += batchGasUsed
    
    return gasUsed, nil
}
```

## Use Case 1: Read-After-Write in Same Batch

**Scenario**: You need to read a value that you've already queued for writing in the same batch.

```go
func updateValidatorWithDependentChanges(evm *vm.EVM, validatorID *big.Int) (uint64, error) {
    batch := NewStateBatch()
    defer batch.Clear()
    
    var gasUsed uint64 = 0
    
    // Update validator stake
    stakeSlot, _ := getValidatorReceivedStakeSlot(validatorID)
    newStake := big.NewInt(2000000)
    batch.SetState(common.BigToHash(stakeSlot), common.BigToHash(newStake))
    
    // Later in the same function, read the updated stake
    currentStake := batch.GetState(evm, common.BigToHash(stakeSlot))
    stakeBigInt := currentStake.Big()
    
    // Calculate commission based on the new stake
    commission := new(big.Int).Div(stakeBigInt, big.NewInt(100)) // 1%
    commissionSlot, _ := getValidatorCommissionSlot(validatorID)
    batch.SetState(common.BigToHash(commissionSlot), common.BigToHash(commission))
    
    return batch.BatchWrite(evm), nil
}
```

## Use Case 2: Conditional Logic Based on Current State

**Scenario**: Implementing complex state transitions that depend on current values.

```go
func conditionalValidatorUpdate(evm *vm.EVM, validatorID *big.Int, action string) (uint64, error) {
    batch := NewStateBatch()
    defer batch.Clear()
    
    var gasUsed uint64 = 0
    
    // Read current validator status
    statusSlot, slotGas := getValidatorStatusSlot(validatorID)
    gasUsed += slotGas
    
    currentStatus := batch.GetState(evm, common.BigToHash(statusSlot))
    statusBigInt := currentStatus.Big()
    
    switch action {
    case "deactivate":
        if statusBigInt.Cmp(big.NewInt(0)) == 0 { // If currently active
            // Deactivate validator
            batch.SetState(common.BigToHash(statusSlot), common.BigToHash(big.NewInt(1)))
            
            // Set deactivation time
            deactivatedTimeSlot, _ := getValidatorDeactivatedTimeSlot(validatorID)
            currentTime := big.NewInt(time.Now().Unix())
            batch.SetState(common.BigToHash(deactivatedTimeSlot), common.BigToHash(currentTime))
            
            // Read current stake to update totals
            stakeSlot, _ := getValidatorReceivedStakeSlot(validatorID)
            currentStake := batch.GetState(evm, common.BigToHash(stakeSlot))
            
            // Update total active stake
            totalActiveStakeSlot := common.BigToHash(big.NewInt(totalActiveStakeSlot))
            currentTotalActive := batch.GetState(evm, totalActiveStakeSlot)
            newTotalActive := new(big.Int).Sub(currentTotalActive.Big(), currentStake.Big())
            batch.SetState(totalActiveStakeSlot, common.BigToHash(newTotalActive))
        }
        
    case "reactivate":
        if statusBigInt.Cmp(big.NewInt(1)) == 0 { // If currently deactivated
            // Reactivate validator
            batch.SetState(common.BigToHash(statusSlot), common.BigToHash(big.NewInt(0)))
            
            // Clear deactivation time
            deactivatedTimeSlot, _ := getValidatorDeactivatedTimeSlot(validatorID)
            batch.SetState(common.BigToHash(deactivatedTimeSlot), common.BigToHash(big.NewInt(0)))
        }
    }
    
    return batch.BatchWrite(evm), nil
}
```

## Use Case 3: Complex Delegation with Reward Stashing

**Scenario**: Delegation function that needs to stash rewards for existing delegations.

```go
func optimizedDelegate(evm *vm.EVM, delegator common.Address, validatorID *big.Int, amount *big.Int) (uint64, error) {
    batch := NewStateBatch()
    defer batch.Clear()
    
    var gasUsed uint64 = 0
    
    // Get delegation slot
    delegationSlot, slotGas := getStakeSlot(delegator, validatorID)
    gasUsed += slotGas
    
    // Read current delegation using GetState
    currentDelegation := batch.GetState(evm, common.BigToHash(delegationSlot))
    currentDelegationBigInt := currentDelegation.Big()
    
    // Check if this is a new delegation
    isNewDelegation := currentDelegationBigInt.Cmp(big.NewInt(0)) == 0
    
    if !isNewDelegation {
        // Existing delegation - stash rewards first
        rewardsSlot, _ := getRewardsStashSlot(delegator, validatorID)
        
        // Read current stashed rewards
        currentRewards := batch.GetState(evm, common.BigToHash(rewardsSlot))
        
        // Calculate pending rewards (simplified calculation)
        pendingRewards := new(big.Int).Mul(currentDelegationBigInt, big.NewInt(100))
        newTotalRewards := new(big.Int).Add(currentRewards.Big(), pendingRewards)
        
        // Update stashed rewards
        batch.SetState(common.BigToHash(rewardsSlot), common.BigToHash(newTotalRewards))
    }
    
    // Update delegation amount
    newDelegation := new(big.Int).Add(currentDelegationBigInt, amount)
    batch.SetState(common.BigToHash(delegationSlot), common.BigToHash(newDelegation))
    
    // Update validator's received stake
    validatorStakeSlot, _ := getValidatorReceivedStakeSlot(validatorID)
    currentValidatorStake := batch.GetState(evm, common.BigToHash(validatorStakeSlot))
    newValidatorStake := new(big.Int).Add(currentValidatorStake.Big(), amount)
    batch.SetState(common.BigToHash(validatorStakeSlot), common.BigToHash(newValidatorStake))
    
    return batch.BatchWrite(evm), nil
}
```

## Use Case 4: Enhanced Getter Functions

**Scenario**: Getter functions that need to return the most current state, including pending changes.

```go
func handleGetValidatorWithPendingChanges(evm *vm.EVM, args []interface{}) ([]byte, uint64, error) {
    validatorID := args[0].(*big.Int)
    batch := NewStateBatch()
    defer batch.Clear()
    
    var gasUsed uint64 = 0
    
    // Get all validator slots
    statusSlot, slotGas := getValidatorStatusSlot(validatorID)
    gasUsed += slotGas
    
    stakeSlot, slotGas := getValidatorReceivedStakeSlot(validatorID)
    gasUsed += slotGas
    
    commissionSlot, slotGas := getValidatorCommissionSlot(validatorID)
    gasUsed += slotGas
    
    // Use GetState to get current values (including any pending writes)
    status := batch.GetState(evm, common.BigToHash(statusSlot))
    stake := batch.GetState(evm, common.BigToHash(stakeSlot))
    commission := batch.GetState(evm, common.BigToHash(commissionSlot))
    
    // Add gas used for reads
    gasUsed += batch.GetGasUsed()
    
    // Pack and return
    result, err := SfcAbi.Methods["getValidator"].Outputs.Pack(
        status.Big(),
        stake.Big(),
        commission.Big(),
    )
    
    return result, gasUsed, err
}
```

## Use Case 5: Epoch Transition with Complex Logic

**Scenario**: Epoch sealing that needs to read and update multiple related values.

```go
func optimizedEpochRewards(evm *vm.EVM, validatorIDs []*big.Int, rewards []*big.Int) (uint64, error) {
    batch := NewStateBatch()
    defer batch.Clear()
    
    var gasUsed uint64 = 0
    
    for i, validatorID := range validatorIDs {
        // Check validator status
        statusSlot, _ := getValidatorStatusSlot(validatorID)
        validatorStatus := batch.GetState(evm, common.BigToHash(statusSlot))
        
        // Only process rewards for active validators
        if validatorStatus.Big().Cmp(big.NewInt(0)) == 0 { // OK_STATUS
            // Read current accumulated rewards
            rewardSlot, _ := getValidatorAccumulatedRewardSlot(validatorID)
            currentRewards := batch.GetState(evm, common.BigToHash(rewardSlot))
            
            // Add new rewards
            newRewards := new(big.Int).Add(currentRewards.Big(), rewards[i])
            batch.SetState(common.BigToHash(rewardSlot), common.BigToHash(newRewards))
            
            // If high performance, give bonus
            if rewards[i].Cmp(big.NewInt(1000)) > 0 {
                stakeSlot, _ := getValidatorReceivedStakeSlot(validatorID)
                currentStake := batch.GetState(evm, common.BigToHash(stakeSlot))
                
                bonus := new(big.Int).Div(rewards[i], big.NewInt(100)) // 1% bonus
                newStake := new(big.Int).Add(currentStake.Big(), bonus)
                batch.SetState(common.BigToHash(stakeSlot), common.BigToHash(newStake))
            }
        }
    }
    
    return batch.BatchWrite(evm), nil
}
```

## Best Practices

### 1. Always Use Batch Operations
```go
// ✅ Good
batch := NewStateBatch()
defer batch.Clear()
value := batch.GetState(evm, slot)

// ❌ Avoid
value := evm.SfcStateDB.GetState(ContractAddress, slot) // Misses pending writes
```

### 2. Handle Gas Costs Properly
```go
// ✅ Good
gasUsed += batch.GetGasUsed() // Track gas from batch operations

// ❌ Avoid
// Forgetting to track gas from batch operations
```

### 3. Use GetState for Conditional Logic
```go
// ✅ Good - Sees pending writes
currentValue := batch.GetState(evm, slot)
if currentValue.Big().Cmp(threshold) > 0 {
    // Logic based on current state
}

// ❌ Avoid - Misses pending writes
currentValue := evm.SfcStateDB.GetState(ContractAddress, slot)
```

### 4. Clean Up Resources
```go
// ✅ Good
batch := NewStateBatch()
defer batch.Clear() // Always clean up

// ✅ Good - Return big.Int to pools
value := GetLargeBigInt()
defer PutLargeBigInt(value)
```

## When NOT to Use GetState

- **When you specifically need the StateDB value**: Use direct StateDB access
- **For read-only operations without pending writes**: Direct access may be more efficient
- **In simple getter functions**: If no complex logic is needed

## Performance Considerations

- **GetState is cached**: Multiple reads of the same slot within a batch are efficient
- **Gas tracking**: GetState automatically tracks gas costs for StateDB reads
- **Memory efficiency**: Use with memory pools for optimal performance
- **Batch size**: Consider gas limits when batching many operations

This integration provides powerful capabilities for complex state management while maintaining efficiency and correctness.
