// Package sfc implements the SFC (Special Fee Contract) precompiled contract.
package sfc

import (
	"fmt"
	"math/big"
	"time"

	"github.com/unicornultrafoundation/go-u2u/common"
)

// VerifyPhase1Integration demonstrates that Phase 1 optimizations are working correctly
func VerifyPhase1Integration() {
	fmt.Println("=== Phase 1 Integration Verification ===")
	
	// Test 1: Enhanced Memory Pools
	fmt.Println("\n1. Testing Enhanced Memory Pools...")
	testMemoryPools()
	
	// Test 2: StateDB Batch Operations
	fmt.Println("\n2. Testing StateDB Batch Operations...")
	testBatchOperations()
	
	// Test 3: Enhanced Slot Calculation Cache
	fmt.Println("\n3. Testing Enhanced Slot Calculation Cache...")
	testSlotCalculationCache()
	
	// Test 4: Integrated Optimizations
	fmt.Println("\n4. Testing Integrated Optimizations...")
	testIntegratedOptimizations()
	
	// Test 5: Performance Metrics
	fmt.Println("\n5. Testing Performance Metrics...")
	testPerformanceMetrics()
	
	fmt.Println("\n=== Phase 1 Integration Verification Complete ===")
}

func testMemoryPools() {
	fmt.Println("  - Testing specialized BigInt pools...")
	
	// Test SmallBigInt pool
	small := GetSmallBigInt()
	small.SetInt64(123)
	fmt.Printf("    SmallBigInt: %s\n", small.String())
	PutSmallBigInt(small)
	
	// Test LargeBigInt pool
	large := GetLargeBigInt()
	large.SetString("123456789012345678901234567890", 10)
	fmt.Printf("    LargeBigInt: %s\n", large.String()[:20]+"...")
	PutLargeBigInt(large)
	
	// Test SlotBigInt pool
	slot := GetSlotBigInt()
	slot.SetInt64(456)
	fmt.Printf("    SlotBigInt: %s\n", slot.String())
	PutSlotBigInt(slot)
	
	// Test byte slice pools
	hashInput := GetHashInput()
	hashInput = append(hashInput, []byte("test hash input")...)
	fmt.Printf("    HashInput: %s\n", string(hashInput))
	PutHashInput(hashInput)
	
	// Test validator ID slice pool
	validatorIDs := GetValidatorIDSlice()
	validatorIDs = append(validatorIDs, big.NewInt(1), big.NewInt(2), big.NewInt(3))
	fmt.Printf("    ValidatorIDs: %d validators\n", len(validatorIDs))
	PutValidatorIDSlice(validatorIDs)
	
	fmt.Println("  ✓ Memory pools working correctly")
}

func testBatchOperations() {
	fmt.Println("  - Testing StateBatch operations...")
	
	batch := NewStateBatch()
	defer batch.Clear()
	
	// Test batch state setting
	slot1 := common.Hash{1}
	value1 := common.Hash{2}
	slot2 := common.Hash{3}
	value2 := common.Hash{4}
	
	batch.SetState(slot1, value1)
	batch.SetState(slot2, value2)
	
	fmt.Printf("    Pending writes: %d\n", batch.GetPendingWriteCount())
	fmt.Printf("    Has pending writes: %t\n", batch.HasPendingWrites())
	
	// Test BatchOperations
	batchOps := NewBatchOperations()
	defer batchOps.Clear()
	
	fmt.Println("  ✓ Batch operations working correctly")
}

func testSlotCalculationCache() {
	fmt.Println("  - Testing enhanced slot calculation cache...")
	
	cache := GetSFCCache()
	validatorID := big.NewInt(1)
	
	// Test cache miss (first calculation)
	start := time.Now()
	slot1, gas1 := cache.GetValidatorSlotFast(validatorID, "status")
	duration1 := time.Since(start)
	
	fmt.Printf("    First calculation - Gas: %d, Duration: %v\n", gas1, duration1)
	
	// Test cache hit (second calculation)
	start = time.Now()
	slot2, gas2 := cache.GetValidatorSlotFast(validatorID, "status")
	duration2 := time.Since(start)
	
	fmt.Printf("    Cached calculation - Gas: %d, Duration: %v\n", gas2, duration2)
	
	// Verify results
	if slot1.Cmp(slot2) == 0 {
		fmt.Println("    ✓ Cached slot matches original")
	} else {
		fmt.Println("    ✗ Cached slot mismatch")
	}
	
	if gas2 == 0 {
		fmt.Println("    ✓ Zero gas for cached calculation")
	} else {
		fmt.Println("    ✗ Non-zero gas for cached calculation")
	}
	
	fmt.Println("  ✓ Slot calculation cache working correctly")
}

func testIntegratedOptimizations() {
	fmt.Println("  - Testing integrated optimizations...")
	
	validatorID := big.NewInt(1)
	
	// Test optimized slot calculation functions
	statusSlot, statusGas := getValidatorStatusSlot(validatorID)
	fmt.Printf("    Status slot calculation - Gas: %d\n", statusGas)
	
	stakeSlot, stakeGas := getValidatorReceivedStakeSlot(validatorID)
	fmt.Printf("    Stake slot calculation - Gas: %d\n", stakeGas)
	
	// Verify slots are different
	if statusSlot.Cmp(stakeSlot) != 0 {
		fmt.Println("    ✓ Different fields have different slots")
	} else {
		fmt.Println("    ✗ Slot collision detected")
	}
	
	// Test validator data caching
	cache := GetSFCCache()
	validatorData := &CachedValidatorData{
		ValidatorID:     validatorID,
		Status:          big.NewInt(0),
		ReceivedStake:   big.NewInt(1000000),
		CreatedEpoch:    big.NewInt(1),
		CreatedTime:     big.NewInt(time.Now().Unix()),
		DeactivatedTime: big.NewInt(0),
	}
	
	// Test cache miss
	cached := cache.GetValidatorData(validatorID)
	if cached == nil {
		fmt.Println("    ✓ Cache miss for new validator")
	} else {
		fmt.Println("    ✗ Unexpected cache hit")
	}
	
	// Test cache set and hit
	cache.SetValidatorData(validatorID, validatorData, 100)
	cached = cache.GetValidatorData(validatorID)
	if cached != nil {
		fmt.Println("    ✓ Cache hit after setting data")
		fmt.Printf("    Cached validator stake: %s\n", cached.ReceivedStake.String())
	} else {
		fmt.Println("    ✗ Cache miss after setting data")
	}
	
	fmt.Println("  ✓ Integrated optimizations working correctly")
}

func testPerformanceMetrics() {
	fmt.Println("  - Testing performance metrics...")
	
	cache := GetSFCCache()
	validatorID := big.NewInt(1)
	
	// Get initial stats
	initialStats := cache.GetCacheStats()
	fmt.Printf("    Initial slot cache hits: %d\n", initialStats.SlotCacheHits)
	fmt.Printf("    Initial slot cache misses: %d\n", initialStats.SlotCacheMisses)
	
	// Perform operations that affect stats
	cache.GetValidatorSlotFast(validatorID, "status")      // Cache miss
	cache.GetValidatorSlotFast(validatorID, "status")      // Cache hit
	cache.GetValidatorSlotFast(validatorID, "receivedStake") // Cache miss
	cache.GetValidatorSlotFast(validatorID, "receivedStake") // Cache hit
	
	// Get updated stats
	updatedStats := cache.GetCacheStats()
	fmt.Printf("    Updated slot cache hits: %d\n", updatedStats.SlotCacheHits)
	fmt.Printf("    Updated slot cache misses: %d\n", updatedStats.SlotCacheMisses)
	
	// Calculate hit rate
	totalOps := updatedStats.SlotCacheHits + updatedStats.SlotCacheMisses
	if totalOps > 0 {
		hitRate := float64(updatedStats.SlotCacheHits) / float64(totalOps) * 100
		fmt.Printf("    Slot cache hit rate: %.2f%%\n", hitRate)
	}
	
	// Test pool statistics
	poolStats := GetPoolStats()
	fmt.Printf("    Pool statistics available: %+v\n", poolStats)
	
	fmt.Println("  ✓ Performance metrics working correctly")
}

// DemoPhase1Performance demonstrates the performance improvements
func DemoPhase1Performance() {
	fmt.Println("\n=== Phase 1 Performance Demonstration ===")
	
	validatorID := big.NewInt(1)
	cache := GetSFCCache()
	
	// Demonstrate slot calculation performance
	fmt.Println("\n1. Slot Calculation Performance:")
	
	// Original calculation
	start := time.Now()
	for i := 0; i < 1000; i++ {
		slot, _ := getValidatorStatusSlot(validatorID)
		_ = slot
	}
	originalDuration := time.Since(start)
	fmt.Printf("   Original (1000 calls): %v\n", originalDuration)
	
	// Cached calculation (first call - cache miss)
	start = time.Now()
	slot, gas := cache.GetValidatorSlotFast(validatorID, "status")
	firstCachedDuration := time.Since(start)
	fmt.Printf("   Cached first call: %v (gas: %d)\n", firstCachedDuration, gas)
	
	// Cached calculation (subsequent calls - cache hits)
	start = time.Now()
	for i := 0; i < 1000; i++ {
		slot, _ = cache.GetValidatorSlotFast(validatorID, "status")
		_ = slot
	}
	cachedDuration := time.Since(start)
	fmt.Printf("   Cached (1000 calls): %v\n", cachedDuration)
	
	if cachedDuration < originalDuration {
		improvement := float64(originalDuration-cachedDuration) / float64(originalDuration) * 100
		fmt.Printf("   Performance improvement: %.2f%%\n", improvement)
	}
	
	// Demonstrate memory pool performance
	fmt.Println("\n2. Memory Pool Performance:")
	
	// Without pools
	start = time.Now()
	for i := 0; i < 10000; i++ {
		val := new(big.Int)
		val.SetInt64(int64(i))
		_ = val
	}
	withoutPoolsDuration := time.Since(start)
	fmt.Printf("   Without pools (10000 allocations): %v\n", withoutPoolsDuration)
	
	// With pools
	start = time.Now()
	for i := 0; i < 10000; i++ {
		val := GetSmallBigInt()
		val.SetInt64(int64(i))
		PutSmallBigInt(val)
	}
	withPoolsDuration := time.Since(start)
	fmt.Printf("   With pools (10000 allocations): %v\n", withPoolsDuration)
	
	if withPoolsDuration < withoutPoolsDuration {
		improvement := float64(withoutPoolsDuration-withPoolsDuration) / float64(withoutPoolsDuration) * 100
		fmt.Printf("   Memory pool improvement: %.2f%%\n", improvement)
	}
	
	fmt.Println("\n=== Performance Demonstration Complete ===")
}
