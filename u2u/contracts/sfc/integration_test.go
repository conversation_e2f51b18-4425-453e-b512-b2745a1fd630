package sfc

import (
	"math/big"
	"testing"

	"github.com/unicornultrafoundation/go-u2u/common"
)

// TestPhase1Integration tests the integration of Phase 1 optimizations
func TestPhase1Integration(t *testing.T) {
	// This test verifies that the Phase 1 optimizations work correctly
	// when integrated into the existing SFC functions

	// Test memory pool integration
	testMemoryPoolIntegration(t)

	// Test batch operations integration
	testBatchOperationsIntegration(t)

	// Test enhanced caching integration
	testEnhancedCachingIntegration(t)
}

func testMemoryPoolIntegration(t *testing.T) {
	// Test that memory pools are properly used in slot calculations
	validatorID := big.NewInt(1)

	// Test getValidatorStatusSlot uses pools
	slot1, gas1 := getValidatorStatusSlot(validatorID)
	if slot1 == nil {
		t.Error("Expected non-nil slot from getValidatorStatusSlot")
	}
	if gas1 == 0 {
		t.Error("Expected non-zero gas from getValidatorStatusSlot")
	}

	// Test getValidatorReceivedStakeSlot uses pools
	slot2, gas2 := getValidatorReceivedStakeSlot(validatorID)
	if slot2 == nil {
		t.Error("Expected non-nil slot from getValidatorReceivedStakeSlot")
	}
	if gas2 == 0 {
		t.Error("Expected non-zero gas from getValidatorReceivedStakeSlot")
	}

	// Slots should be different
	if slot1.Cmp(slot2) == 0 {
		t.Error("Different validator fields should have different slots")
	}
}

func testBatchOperationsIntegration(t *testing.T) {
	// Test that batch operations work correctly
	batch := NewStateBatch()
	defer batch.Clear()

	// Test setting multiple states
	slot1 := common.Hash{1}
	value1 := common.Hash{2}
	slot2 := common.Hash{3}
	value2 := common.Hash{4}

	batch.SetState(slot1, value1)
	batch.SetState(slot2, value2)

	// Verify pending writes
	if batch.GetPendingWriteCount() != 2 {
		t.Errorf("Expected 2 pending writes, got %d", batch.GetPendingWriteCount())
	}

	// Test BatchOperations helper
	batchOps := NewBatchOperations()
	defer batchOps.Clear()

	// Test that it doesn't panic
	batchOps.Clear()
}

func testEnhancedCachingIntegration(t *testing.T) {
	// Test that enhanced caching works correctly
	cache := GetSFCCache()
	validatorID := big.NewInt(1)

	// Test validator slot caching
	slot1, _ := cache.GetValidatorSlotFast(validatorID, "status")
	if slot1 == nil {
		t.Error("Expected non-nil slot from GetValidatorSlotFast")
	}

	// Second call should be cached (0 gas)
	slot2, gas2 := cache.GetValidatorSlotFast(validatorID, "status")
	if slot2 == nil {
		t.Error("Expected non-nil slot from cached GetValidatorSlotFast")
	}
	if gas2 != 0 {
		t.Error("Expected zero gas for cached slot calculation")
	}
	if slot1.Cmp(slot2) != 0 {
		t.Error("Cached slot should match original slot")
	}

	// Test validator data caching
	validatorData := &CachedValidatorData{
		ValidatorID:     validatorID,
		Status:          big.NewInt(0),
		ReceivedStake:   big.NewInt(1000000),
		CreatedEpoch:    big.NewInt(1),
		CreatedTime:     big.NewInt(1234567890),
		DeactivatedTime: big.NewInt(0),
	}

	// Cache miss
	if cached := cache.GetValidatorData(validatorID); cached != nil {
		t.Error("Expected cache miss for new validator")
	}

	// Set and get
	cache.SetValidatorData(validatorID, validatorData, 100)
	if cached := cache.GetValidatorData(validatorID); cached == nil {
		t.Error("Expected cache hit after setting validator data")
	}
}

// TestOptimizedFunctionPerformance compares optimized vs original function performance
func TestOptimizedFunctionPerformance(t *testing.T) {
	// This test would ideally compare performance, but since we don't have
	// a real EVM context in unit tests, we'll just verify the functions work

	validatorID := big.NewInt(777) // Use unique validator ID
	cache := GetSFCCache()

	// Test that optimized slot calculation works
	slot, _ := cache.GetValidatorSlotFast(validatorID, "status")
	if slot == nil {
		t.Error("Optimized slot calculation failed")
	}

	// Test cache hit
	slot2, gas2 := cache.GetValidatorSlotFast(validatorID, "status")
	if slot2 == nil {
		t.Error("Cached slot calculation failed")
	}
	if gas2 != 0 {
		t.Error("Expected zero gas for cached calculation")
	}

	// Verify slots match
	if slot.Cmp(slot2) != 0 {
		t.Error("Cached slot should match original slot")
	}
}

// TestBatchVsIndividualOperations tests batch operations vs individual operations
func TestBatchVsIndividualOperations(t *testing.T) {
	// Test individual operations
	validatorID := big.NewInt(1)

	// Individual slot calculations
	statusSlot, statusGas := getValidatorStatusSlot(validatorID)
	stakeSlot, stakeGas := getValidatorReceivedStakeSlot(validatorID)

	totalIndividualGas := statusGas + stakeGas

	// Batch operations using cache
	cache := GetSFCCache()

	// First call (cache miss)
	cachedStatusSlot, cachedStatusGas := cache.GetValidatorSlotFast(validatorID, "status")
	cachedStakeSlot, cachedStakeGas := cache.GetValidatorSlotFast(validatorID, "receivedStake")

	totalCachedGas := cachedStatusGas + cachedStakeGas

	// Verify results are the same
	if statusSlot.Cmp(cachedStatusSlot) != 0 {
		t.Error("Cached status slot should match individual calculation")
	}
	if stakeSlot.Cmp(cachedStakeSlot) != 0 {
		t.Error("Cached stake slot should match individual calculation")
	}

	// Gas usage should be similar for first calculation
	if totalCachedGas != totalIndividualGas {
		t.Logf("Individual gas: %d, Cached gas: %d", totalIndividualGas, totalCachedGas)
		// This is expected to be different due to optimization overhead
	}

	// Second call should be much cheaper
	_, cachedStatusGas2 := cache.GetValidatorSlotFast(validatorID, "status")
	_, cachedStakeGas2 := cache.GetValidatorSlotFast(validatorID, "receivedStake")

	totalCachedGas2 := cachedStatusGas2 + cachedStakeGas2

	if totalCachedGas2 != 0 {
		t.Error("Expected zero gas for fully cached calculations")
	}
}

// TestMemoryPoolEfficiency tests memory pool efficiency
func TestMemoryPoolEfficiency(t *testing.T) {
	// Test that pools reduce allocations

	// Use pools extensively
	for i := 0; i < 100; i++ {
		// Small BigInt operations
		val1 := GetSmallBigInt()
		val1.SetInt64(int64(i))
		PutSmallBigInt(val1)

		// Large BigInt operations
		val2 := GetLargeBigInt()
		val2.SetString("123456789012345678901234567890", 10)
		PutLargeBigInt(val2)

		// Slot BigInt operations
		val3 := GetSlotBigInt()
		val3.SetInt64(int64(i * 1000))
		PutSlotBigInt(val3)

		// Byte slice operations
		bytes1 := GetHashInput()
		bytes1 = append(bytes1, []byte("test")...)
		PutHashInput(bytes1)

		// Validator ID slice operations
		slice1 := GetValidatorIDSlice()
		slice1 = append(slice1, big.NewInt(int64(i)))
		PutValidatorIDSlice(slice1)
	}

	// If we get here without panicking, pools are working
	t.Log("Memory pool efficiency test completed successfully")
}

// TestCacheStatistics tests cache statistics tracking
func TestCacheStatistics(t *testing.T) {
	cache := GetSFCCache()
	validatorID := big.NewInt(999) // Use unique validator ID to avoid cache conflicts

	// Get initial stats
	initialStats := cache.GetCacheStats()

	// Perform operations that should affect stats
	cache.GetValidatorSlotFast(validatorID, "status")        // Cache miss
	cache.GetValidatorSlotFast(validatorID, "status")        // Cache hit
	cache.GetValidatorSlotFast(validatorID, "receivedStake") // Cache miss
	cache.GetValidatorSlotFast(validatorID, "receivedStake") // Cache hit

	// Get updated stats
	updatedStats := cache.GetCacheStats()

	// Verify stats increased (exact numbers may vary due to other tests)
	if updatedStats.SlotCacheHits <= initialStats.SlotCacheHits {
		t.Error("Expected slot cache hits to increase")
	}

	if updatedStats.SlotCacheMisses <= initialStats.SlotCacheMisses {
		t.Error("Expected slot cache misses to increase")
	}
}

// BenchmarkPhase1Optimizations benchmarks the Phase 1 optimizations
func BenchmarkPhase1Optimizations(b *testing.B) {
	validatorID := big.NewInt(1)
	cache := GetSFCCache()

	b.Run("OriginalSlotCalculation", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			slot, gas := getValidatorStatusSlot(validatorID)
			_ = slot
			_ = gas
		}
	})

	b.Run("CachedSlotCalculation", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			slot, gas := cache.GetValidatorSlotFast(validatorID, "status")
			_ = slot
			_ = gas
		}
	})

	b.Run("MemoryPoolOperations", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			val := GetSmallBigInt()
			val.SetInt64(int64(i))
			PutSmallBigInt(val)
		}
	})

	b.Run("BatchOperations", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			batch := NewStateBatch()
			batch.SetState(common.Hash{byte(i)}, common.Hash{byte(i + 1)})
			batch.Clear()
		}
	})
}
