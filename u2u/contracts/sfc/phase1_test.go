package sfc

import (
	"math/big"
	"testing"
	"time"

	"github.com/unicornultrafoundation/go-u2u/common"
)

// TestEnhancedMemoryPools tests the specialized memory pools
func TestEnhancedMemoryPools(t *testing.T) {
	// Test SmallBigInt pool
	small1 := GetSmallBigInt()
	small1.SetInt64(123)
	small2 := GetSmallBigInt()
	small2.SetInt64(456)
	
	PutSmallBigInt(small1)
	PutSmallBigInt(small2)
	
	// Test LargeBigInt pool
	large1 := GetLargeBigInt()
	large1.SetString("123456789012345678901234567890", 10)
	large2 := GetLargeBigInt()
	large2.SetString("987654321098765432109876543210", 10)
	
	PutLargeBigInt(large1)
	PutLargeBigInt(large2)
	
	// Test SlotBigInt pool
	slot1 := GetSlotBigInt()
	slot1.SetInt64(789)
	slot2 := GetSlotBigInt()
	slot2.SetInt64(101112)
	
	PutSlotBigInt(slot1)
	PutSlotBigInt(slot2)
	
	// Test byte slice pools
	hashInput1 := GetHashInput()
	hashInput1 = append(hashInput1, []byte("test hash input")...)
	PutHashInput(hashInput1)
	
	largeBytes1 := GetLargeByteSlice()
	largeBytes1 = append(largeBytes1, make([]byte, 100)...)
	PutLargeByteSlice(largeBytes1)
	
	// Test slice pools
	validatorIDs := GetValidatorIDSlice()
	validatorIDs = append(validatorIDs, big.NewInt(1), big.NewInt(2), big.NewInt(3))
	PutValidatorIDSlice(validatorIDs)
	
	hashes := GetHashSlice()
	hashes = append(hashes, common.Hash{1}, common.Hash{2})
	PutHashSlice(hashes)
	
	uptimes := GetUptimeSlice()
	uptimes = append(uptimes, big.NewInt(100), big.NewInt(200))
	PutUptimeSlice(uptimes)
}

// TestStateBatch tests the batch operations functionality
func TestStateBatch(t *testing.T) {
	batch := NewStateBatch()
	
	// Test batch state setting
	slot1 := common.Hash{1}
	value1 := common.Hash{2}
	slot2 := common.Hash{3}
	value2 := common.Hash{4}
	
	batch.SetState(slot1, value1)
	batch.SetState(slot2, value2)
	
	// Check pending writes
	if !batch.HasPendingWrites() {
		t.Error("Expected pending writes")
	}
	
	if batch.GetPendingWriteCount() != 2 {
		t.Errorf("Expected 2 pending writes, got %d", batch.GetPendingWriteCount())
	}
	
	// Test clear
	batch.Clear()
	
	if batch.HasPendingWrites() {
		t.Error("Expected no pending writes after clear")
	}
	
	if batch.GetPendingWriteCount() != 0 {
		t.Errorf("Expected 0 pending writes after clear, got %d", batch.GetPendingWriteCount())
	}
}

// TestBatchOperations tests the high-level batch operations
func TestBatchOperations(t *testing.T) {
	batchOps := NewBatchOperations()
	
	// Test that batch operations can be created and cleared
	batchOps.Clear()
	
	// Test batch operations don't panic
	defer func() {
		if r := recover(); r != nil {
			t.Errorf("Batch operations panicked: %v", r)
		}
	}()
	
	batchOps.Clear()
}

// TestEnhancedCache tests the enhanced caching functionality
func TestEnhancedCache(t *testing.T) {
	cache := NewSFCCache()
	
	// Test validator data caching
	validatorID := big.NewInt(1)
	validatorData := &CachedValidatorData{
		ValidatorID:     validatorID,
		Status:          big.NewInt(0),
		Commission:      big.NewInt(1000),
		ReceivedStake:   big.NewInt(1000000),
		CreatedEpoch:    big.NewInt(1),
		CreatedTime:     big.NewInt(time.Now().Unix()),
		DeactivatedTime: big.NewInt(0),
	}
	
	// Test cache miss
	if cached := cache.GetValidatorData(validatorID); cached != nil {
		t.Error("Expected cache miss for new validator")
	}
	
	// Test cache set and hit
	cache.SetValidatorData(validatorID, validatorData, 100)
	if cached := cache.GetValidatorData(validatorID); cached == nil {
		t.Error("Expected cache hit after setting validator data")
	} else {
		if cached.ValidatorID.Cmp(validatorID) != 0 {
			t.Error("Cached validator ID doesn't match")
		}
		if cached.AccessCount != 1 {
			t.Errorf("Expected access count 1, got %d", cached.AccessCount)
		}
	}
	
	// Test epoch data caching
	epochData := &CachedEpochData{
		EpochNumber:     1,
		SnapshotSlot:    big.NewInt(123),
		ValidatorIDs:    []*big.Int{validatorID},
		TotalStake:      big.NewInt(1000000),
		TotalBaseReward: big.NewInt(100),
		TotalTxReward:   big.NewInt(50),
		ValidatorData:   make(map[string]*ValidatorEpochData),
	}
	
	// Test epoch cache miss
	if cached := cache.GetEpochData(1); cached != nil {
		t.Error("Expected cache miss for new epoch")
	}
	
	// Test epoch cache set and hit
	cache.SetEpochData(1, epochData, 100)
	if cached := cache.GetEpochData(1); cached == nil {
		t.Error("Expected cache hit after setting epoch data")
	} else {
		if cached.EpochNumber != 1 {
			t.Error("Cached epoch number doesn't match")
		}
		if cached.AccessCount != 1 {
			t.Errorf("Expected access count 1, got %d", cached.AccessCount)
		}
	}
}

// TestSlotCalculatorCache tests the optimized slot calculation
func TestSlotCalculatorCache(t *testing.T) {
	cache := NewSFCCache()
	validatorID := big.NewInt(1)
	
	// Test fast slot calculation for known fields
	slot1, gas1 := cache.GetValidatorSlotFast(validatorID, "status")
	if slot1 == nil {
		t.Error("Expected non-nil slot for status field")
	}
	if gas1 == 0 {
		t.Error("Expected non-zero gas for first calculation")
	}
	
	// Test cache hit (should return 0 gas)
	slot2, gas2 := cache.GetValidatorSlotFast(validatorID, "status")
	if slot2 == nil {
		t.Error("Expected non-nil slot for cached status field")
	}
	if gas2 != 0 {
		t.Error("Expected zero gas for cached calculation")
	}
	
	// Slots should be the same
	if slot1.Cmp(slot2) != 0 {
		t.Error("Cached slot should match original slot")
	}
	
	// Test different field
	slot3, gas3 := cache.GetValidatorSlotFast(validatorID, "receivedStake")
	if slot3 == nil {
		t.Error("Expected non-nil slot for receivedStake field")
	}
	if gas3 == 0 {
		t.Error("Expected non-zero gas for first receivedStake calculation")
	}
	
	// Different fields should have different slots
	if slot1.Cmp(slot3) == 0 {
		t.Error("Different fields should have different slots")
	}
}

// TestCacheStats tests cache statistics tracking
func TestCacheStats(t *testing.T) {
	cache := NewSFCCache()
	validatorID := big.NewInt(1)
	
	// Get initial stats
	initialStats := cache.GetCacheStats()
	
	// Perform some operations that should affect stats
	cache.GetValidatorSlotFast(validatorID, "status")  // Cache miss
	cache.GetValidatorSlotFast(validatorID, "status")  // Cache hit
	
	// Get updated stats
	updatedStats := cache.GetCacheStats()
	
	// Check that stats were updated
	if updatedStats.SlotCacheHits <= initialStats.SlotCacheHits {
		t.Error("Expected slot cache hits to increase")
	}
	
	if updatedStats.SlotCacheMisses <= initialStats.SlotCacheMisses {
		t.Error("Expected slot cache misses to increase")
	}
}

// TestCacheClear tests cache clearing functionality
func TestCacheClear(t *testing.T) {
	cache := NewSFCCache()
	validatorID := big.NewInt(1)
	
	// Add some data to cache
	validatorData := &CachedValidatorData{
		ValidatorID: validatorID,
		Status:      big.NewInt(0),
	}
	cache.SetValidatorData(validatorID, validatorData, 100)
	
	epochData := &CachedEpochData{
		EpochNumber: 1,
		ValidatorData: make(map[string]*ValidatorEpochData),
	}
	cache.SetEpochData(1, epochData, 100)
	
	// Verify data is cached
	if cache.GetValidatorData(validatorID) == nil {
		t.Error("Expected validator data to be cached")
	}
	if cache.GetEpochData(1) == nil {
		t.Error("Expected epoch data to be cached")
	}
	
	// Clear epoch cache
	cache.ClearEpochCache()
	
	// Validator data should still be there, but epoch-specific data may be cleared
	if cache.GetValidatorData(validatorID) == nil {
		t.Error("Expected validator data to remain after epoch cache clear")
	}
}

// BenchmarkMemoryPools benchmarks the memory pool performance
func BenchmarkMemoryPools(b *testing.B) {
	b.Run("SmallBigInt", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			val := GetSmallBigInt()
			val.SetInt64(int64(i))
			PutSmallBigInt(val)
		}
	})
	
	b.Run("LargeBigInt", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			val := GetLargeBigInt()
			val.SetInt64(int64(i))
			PutLargeBigInt(val)
		}
	})
	
	b.Run("ValidatorIDSlice", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			slice := GetValidatorIDSlice()
			slice = append(slice, big.NewInt(int64(i)))
			PutValidatorIDSlice(slice)
		}
	})
}

// BenchmarkCacheOperations benchmarks cache performance
func BenchmarkCacheOperations(b *testing.B) {
	cache := NewSFCCache()
	validatorID := big.NewInt(1)
	
	b.Run("SlotCalculation", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			cache.GetValidatorSlotFast(validatorID, "status")
		}
	})
	
	b.Run("ValidatorDataCache", func(b *testing.B) {
		validatorData := &CachedValidatorData{
			ValidatorID: validatorID,
			Status:      big.NewInt(0),
		}
		cache.SetValidatorData(validatorID, validatorData, 100)
		
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			cache.GetValidatorData(validatorID)
		}
	})
}
