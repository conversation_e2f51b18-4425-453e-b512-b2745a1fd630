// Package sfc implements the SFC (Special Fee Contract) precompiled contract.
package sfc

import (
	"math/big"

	"github.com/unicornultrafoundation/go-u2u/common"
	"github.com/unicornultrafoundation/go-u2u/core/vm"
)

// Example usage of Phase 1 optimizations
// This file demonstrates how to use the enhanced memory pools, batch operations, and caching

// OptimizedGetValidator demonstrates optimized validator data retrieval
func OptimizedGetValidator(evm *vm.EVM, validatorID *big.Int) (map[string]*big.Int, uint64, error) {
	var gasUsed uint64 = 0
	cache := GetSFCCache()

	// Try to get from cache first
	if cachedData := cache.GetValidatorData(validatorID); cachedData != nil {
		// Cache hit - return cached data
		result := map[string]*big.Int{
			"status":          cachedData.Status,
			"commission":      cachedData.Commission,
			"receivedStake":   cachedData.ReceivedStake,
			"createdEpoch":    cachedData.CreatedEpoch,
			"createdTime":     cachedData.CreatedTime,
			"deactivatedTime": cachedData.DeactivatedTime,
		}
		return result, 0, nil // No gas cost for cache hit
	}

	// Cache miss - use batch operations to read all validator data
	batch := NewStateBatch()

	// Calculate all validator slots using enhanced caching
	statusSlot, statusGas := cache.GetValidatorSlotFast(validatorID, "status")
	gasUsed += statusGas

	receivedStakeSlot, stakeGas := cache.GetValidatorSlotFast(validatorID, "receivedStake")
	gasUsed += stakeGas

	createdEpochSlot, epochGas := cache.GetValidatorSlotFast(validatorID, "createdEpoch")
	gasUsed += epochGas

	createdTimeSlot, timeGas := cache.GetValidatorSlotFast(validatorID, "createdTime")
	gasUsed += timeGas

	deactivatedTimeSlot, deactGas := cache.GetValidatorSlotFast(validatorID, "deactivatedTime")
	gasUsed += deactGas

	// Batch read all slots
	slots := GetHashSlice()
	defer PutHashSlice(slots)

	slots = append(slots,
		common.BigToHash(statusSlot),
		common.BigToHash(receivedStakeSlot),
		common.BigToHash(createdEpochSlot),
		common.BigToHash(createdTimeSlot),
		common.BigToHash(deactivatedTimeSlot),
	)

	results := batch.BatchRead(evm, slots)
	gasUsed += batch.GetGasUsed()

	// Convert results to big.Int using pools
	status := GetSmallBigInt().SetBytes(results[slots[0]].Bytes())
	receivedStake := GetLargeBigInt().SetBytes(results[slots[1]].Bytes())
	createdEpoch := GetSmallBigInt().SetBytes(results[slots[2]].Bytes())
	createdTime := GetSmallBigInt().SetBytes(results[slots[3]].Bytes())
	deactivatedTime := GetSmallBigInt().SetBytes(results[slots[4]].Bytes())

	// Cache the results for future use
	cachedData := &CachedValidatorData{
		ValidatorID:         validatorID,
		Status:              status,
		ReceivedStake:       receivedStake,
		CreatedEpoch:        createdEpoch,
		CreatedTime:         createdTime,
		DeactivatedTime:     deactivatedTime,
		StatusSlot:          statusSlot,
		ReceivedStakeSlot:   receivedStakeSlot,
		CreatedEpochSlot:    createdEpochSlot,
		CreatedTimeSlot:     createdTimeSlot,
		DeactivatedTimeSlot: deactivatedTimeSlot,
	}

	cache.SetValidatorData(validatorID, cachedData, evm.Context.BlockNumber.Uint64())

	result := map[string]*big.Int{
		"status":          status,
		"receivedStake":   receivedStake,
		"createdEpoch":    createdEpoch,
		"createdTime":     createdTime,
		"deactivatedTime": deactivatedTime,
	}

	return result, gasUsed, nil
}

// OptimizedBatchValidatorUpdate demonstrates optimized batch validator updates
func OptimizedBatchValidatorUpdate(evm *vm.EVM, updates map[string]map[string]*big.Int) (uint64, error) {
	var totalGasUsed uint64 = 0

	// Use batch operations for efficient writes
	batchOps := NewBatchOperations()
	defer batchOps.Clear()

	for validatorIDStr, updateData := range updates {
		validatorID := GetSmallBigInt()
		validatorID.SetString(validatorIDStr, 10)

		// Convert common.Hash values for batch writing
		hashData := make(map[string]common.Hash)
		for field, value := range updateData {
			hashData[field] = common.BigToHash(value)
		}

		// Use batch operations to write validator data
		gasUsed := batchOps.WriteValidatorData(evm, validatorID, hashData)
		totalGasUsed += gasUsed

		PutSmallBigInt(validatorID)
	}

	// Flush all pending writes
	flushGas := batchOps.Flush(evm)
	totalGasUsed += flushGas

	return totalGasUsed, nil
}

// OptimizedEpochDataRetrieval demonstrates optimized epoch data access
func OptimizedEpochDataRetrieval(evm *vm.EVM, epoch *big.Int, validatorIDs []*big.Int) (map[common.Hash]common.Hash, uint64, error) {
	var gasUsed uint64 = 0
	cache := GetSFCCache()

	epochNum := epoch.Uint64()

	// Try to get from cache first
	if cachedEpochData := cache.GetEpochData(epochNum); cachedEpochData != nil {
		// Cache hit - use batch operations to get current data
		batchOps := NewBatchOperations()
		defer batchOps.Clear()

		results, batchGas := batchOps.ReadEpochData(evm, epoch, validatorIDs)
		gasUsed += batchGas

		return results, gasUsed, nil
	}

	// Cache miss - read and cache epoch data
	batchOps := NewBatchOperations()
	defer batchOps.Clear()

	results, batchGas := batchOps.ReadEpochData(evm, epoch, validatorIDs)
	gasUsed += batchGas

	// Create cached epoch data
	epochSnapshotSlot, _ := getEpochSnapshotSlot(epoch)

	cachedEpochData := &CachedEpochData{
		EpochNumber:   epochNum,
		SnapshotSlot:  epochSnapshotSlot,
		ValidatorIDs:  validatorIDs,
		ValidatorData: make(map[string]*ValidatorEpochData),
		LastUpdated:   evm.Context.BlockNumber.Uint64(),
	}

	// Cache validator-specific epoch data
	for _, validatorID := range validatorIDs {
		validatorKey := validatorID.String()

		accumulatedRewardSlot, _ := getEpochValidatorAccumulatedRewardPerTokenSlot(epoch, validatorID)
		accumulatedUptimeSlot, _ := getEpochValidatorAccumulatedUptimeSlot(epoch, validatorID)
		accumulatedTxsFeeSlot, _ := getEpochValidatorAccumulatedOriginatedTxsFeeSlot(epoch, validatorID)
		offlineTimeSlot, _ := getEpochValidatorOfflineTimeSlot(epoch, validatorID)
		offlineBlocksSlot, _ := getEpochValidatorOfflineBlocksSlot(epoch, validatorID)

		cachedEpochData.ValidatorData[validatorKey] = &ValidatorEpochData{
			AccumulatedRewardPerTokenSlot:   accumulatedRewardSlot,
			AccumulatedOriginatedTxsFeeSlot: accumulatedTxsFeeSlot,
			AccumulatedUptimeSlot:           accumulatedUptimeSlot,
			OfflineTimeSlot:                 offlineTimeSlot,
			OfflineBlocksSlot:               offlineBlocksSlot,
		}
	}

	cache.SetEpochData(epochNum, cachedEpochData, evm.Context.BlockNumber.Uint64())

	return results, gasUsed, nil
}

// OptimizedMemoryUsageExample demonstrates proper memory pool usage
func OptimizedMemoryUsageExample() {
	// Use specialized pools for different purposes

	// For small values (validator IDs, epochs, etc.)
	validatorID := GetSmallBigInt()
	validatorID.SetInt64(1)
	defer PutSmallBigInt(validatorID)

	// For large values (stakes, rewards, etc.)
	stakeAmount := GetLargeBigInt()
	stakeAmount.SetString("1000000000000000000000", 10) // 1000 tokens
	defer PutLargeBigInt(stakeAmount)

	// For slot calculations
	slot := GetSlotBigInt()
	slot.SetInt64(123)
	defer PutSlotBigInt(slot)

	// For hash inputs
	hashInput := GetHashInput()
	defer PutHashInput(hashInput)

	// For validator ID slices
	validatorIDs := GetValidatorIDSlice()
	validatorIDs = append(validatorIDs, validatorID)
	defer PutValidatorIDSlice(validatorIDs)

	// For hash slices
	hashes := GetHashSlice()
	hashes = append(hashes, common.BigToHash(slot))
	defer PutHashSlice(hashes)
}

// GetPhase1PerformanceMetrics returns performance metrics for Phase 1 optimizations
func GetPhase1PerformanceMetrics() map[string]interface{} {
	cache := GetSFCCache()
	stats := cache.GetCacheStats()
	poolStats := GetPoolStats()

	return map[string]interface{}{
		"cache_stats": map[string]interface{}{
			"validator_cache_hit_rate": float64(stats.ValidatorCacheHits) / float64(stats.ValidatorCacheHits+stats.ValidatorCacheMisses),
			"epoch_cache_hit_rate":     float64(stats.EpochCacheHits) / float64(stats.EpochCacheHits+stats.EpochCacheMisses),
			"slot_cache_hit_rate":      float64(stats.SlotCacheHits) / float64(stats.SlotCacheHits+stats.SlotCacheMisses),
			"hash_cache_hit_rate":      float64(stats.HashCacheHits) / float64(stats.HashCacheHits+stats.HashCacheMisses),
			"total_operations":         stats.TotalOperations,
		},
		"pool_stats":          poolStats,
		"optimization_status": "Phase 1 - Foundation Optimizations Active",
	}
}
