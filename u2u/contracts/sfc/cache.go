// Package sfc implements the SFC (Special Fee Contract) precompiled contract.
package sfc

import (
	"fmt"
	"math/big"
	"strconv"
	"sync"

	"github.com/unicornultrafoundation/go-u2u/common"
	"github.com/unicornultrafoundation/go-u2u/crypto"
)

// HashCache stores previously calculated hashes to avoid redundant calculations
type HashCache struct {
	// Map from input bytes to calculated hash
	cache map[string]common.Hash
}

// NewHashCache creates a new hash cache
func NewHashCache() *HashCache {
	return &HashCache{
		cache: make(map[string]common.Hash),
	}
}

// GetOrCompute gets a hash from the cache or computes it if not found
func (c *HashCache) GetOrCompute(input []byte) common.Hash {
	// Convert input to string for map key
	key := string(input)

	if hash, found := c.cache[key]; found {
		return hash
	}

	// Compute hash if not in cache
	hash := crypto.Keccak256Hash(input)

	// Store in cache
	c.cache[key] = hash

	return hash
}

// CachedKeccak256 computes the Keccak256 hash of the input using the cache
func (c *HashCache) CachedKeccak256(input []byte) []byte {
	return c.GetOrCompute(input).Bytes()
}

// CachedKeccak256Hash computes the Keccak256 hash of the input using the cache
func (c *HashCache) CachedKeccak256Hash(input []byte) common.Hash {
	return c.GetOrCompute(input)
}

// SlotCache stores previously calculated storage slots
type SlotCache struct {
	// Map from string representation of inputs to calculated slots
	cache map[string]*big.Int
}

// NewSlotCache creates a new slot cache
func NewSlotCache() *SlotCache {
	return &SlotCache{
		cache: make(map[string]*big.Int),
	}
}

// GetOrCompute gets a slot from the cache or computes it using the provided function
func (c *SlotCache) GetOrCompute(key string, computeFunc func() (*big.Int, uint64)) (*big.Int, uint64) {
	if slot, found := c.cache[key]; found {
		return slot, 0 // No gas used for cache hit
	}

	// Compute if not found
	slot, gasUsed := computeFunc()

	// Store in cache
	c.cache[key] = slot

	return slot, gasUsed
}

// Enhanced cache data structures for Phase 1 optimizations

// CachedValidatorData stores frequently accessed validator information
type CachedValidatorData struct {
	ValidatorID     *big.Int
	Status          *big.Int
	Commission      *big.Int
	ReceivedStake   *big.Int
	CreatedEpoch    *big.Int
	CreatedTime     *big.Int
	DeactivatedTime *big.Int

	// Pre-calculated slots for fast access
	StatusSlot          *big.Int
	CommissionSlot      *big.Int
	ReceivedStakeSlot   *big.Int
	CreatedEpochSlot    *big.Int
	CreatedTimeSlot     *big.Int
	DeactivatedTimeSlot *big.Int

	// Cache metadata
	LastUpdated uint64 // Block number when last updated
	AccessCount uint64 // Number of times accessed
}

// CachedEpochData stores epoch-specific cached information
type CachedEpochData struct {
	EpochNumber     uint64
	SnapshotSlot    *big.Int
	ValidatorIDs    []*big.Int
	TotalStake      *big.Int
	TotalBaseReward *big.Int
	TotalTxReward   *big.Int

	// Validator-specific epoch data
	ValidatorData map[string]*ValidatorEpochData

	// Cache metadata
	LastUpdated uint64
	AccessCount uint64
}

// ValidatorEpochData stores validator-specific data for an epoch
type ValidatorEpochData struct {
	AccumulatedRewardPerTokenSlot   *big.Int
	AccumulatedOriginatedTxsFeeSlot *big.Int
	AccumulatedUptimeSlot           *big.Int
	OfflineTimeSlot                 *big.Int
	OfflineBlocksSlot               *big.Int
}

// SlotCalculatorCache provides optimized slot calculations
type SlotCalculatorCache struct {
	// Pre-computed base slots
	ValidatorBaseSlot     *big.Int
	StakeBaseSlot         *big.Int
	RewardsBaseSlot       *big.Int
	EpochSnapshotBaseSlot *big.Int

	// Pre-computed offsets
	Offsets map[string]int64

	// Cached hash inputs
	HashInputCache map[string][]byte

	// Cached slots for current epoch
	CurrentEpochSlots map[string]*big.Int

	// Mutex for thread safety
	mutex sync.RWMutex
}

// CacheStats tracks cache performance metrics
type CacheStats struct {
	// Hit/miss ratios
	HashCacheHits   uint64
	HashCacheMisses uint64
	SlotCacheHits   uint64
	SlotCacheMisses uint64

	// Validator cache stats
	ValidatorCacheHits   uint64
	ValidatorCacheMisses uint64

	// Epoch cache stats
	EpochCacheHits   uint64
	EpochCacheMisses uint64

	// Total operations
	TotalOperations uint64

	// Mutex for thread safety
	mutex sync.RWMutex
}

// SFCCache contains all caches used by the SFC package
type SFCCache struct {
	Hash *HashCache
	Slot *SlotCache

	// Specialized caches for common operations
	ValidatorSlot map[string]*big.Int
	EpochSlot     map[string]*big.Int

	// Hash input caches
	HashInputs        map[string][]byte // Cache for hash inputs (validatorID + slot)
	AddressHashInputs map[string][]byte // Cache for address hash inputs (address + slot)
	NestedHashInputs  map[string][]byte // Cache for nested hash inputs

	// Unified ABI encoding cache
	AbiPackCache map[string][]byte // Cache for all ABI packed results

	// Enhanced caches for Phase 1 optimizations
	ValidatorDataCache  map[string]*CachedValidatorData
	EpochDataCache      map[uint64]*CachedEpochData
	SlotCalculatorCache *SlotCalculatorCache

	// Cache statistics
	Stats *CacheStats

	// Mutex for thread safety
	mutex sync.RWMutex
}

// NewSFCCache creates a new SFC cache
func NewSFCCache() *SFCCache {
	cache := &SFCCache{
		Hash:              NewHashCache(),
		Slot:              NewSlotCache(),
		ValidatorSlot:     make(map[string]*big.Int),
		EpochSlot:         make(map[string]*big.Int),
		HashInputs:        make(map[string][]byte),
		AddressHashInputs: make(map[string][]byte),
		NestedHashInputs:  make(map[string][]byte),
		AbiPackCache:      make(map[string][]byte),

		// Initialize enhanced caches
		ValidatorDataCache:  make(map[string]*CachedValidatorData),
		EpochDataCache:      make(map[uint64]*CachedEpochData),
		SlotCalculatorCache: NewSlotCalculatorCache(),
		Stats:               NewCacheStats(),
	}

	return cache
}

// NewSlotCalculatorCache creates a new slot calculator cache
func NewSlotCalculatorCache() *SlotCalculatorCache {
	return &SlotCalculatorCache{
		ValidatorBaseSlot:     GetSmallBigInt().SetInt64(validatorSlot),
		StakeBaseSlot:         GetSmallBigInt().SetInt64(stakeSlot),
		RewardsBaseSlot:       GetSmallBigInt().SetInt64(rewardsStashSlot),
		EpochSnapshotBaseSlot: GetSmallBigInt().SetInt64(epochSnapshotSlot),
		Offsets: map[string]int64{
			// Validator struct field offsets (based on Solidity struct layout)
			// uint256 status; (offset 0)
			// uint256 deactivatedTime; (offset 1)
			// uint256 deactivatedEpoch; (offset 2)
			// uint256 receivedStake; (offset 3)
			// uint256 createdEpoch; (offset 4)
			// uint256 createdTime; (offset 5)
			// address auth; (offset 6)
			"status":           0,
			"deactivatedTime":  1,
			"deactivatedEpoch": 2,
			"receivedStake":    3,
			"createdEpoch":     4,
			"createdTime":      5,
			"auth":             6,

			// EpochSnapshot struct field offsets
			"offlineTime":   offlineTimeOffset,
			"offlineBlocks": offlineBlocksOffset,
		},
		HashInputCache:    make(map[string][]byte),
		CurrentEpochSlots: make(map[string]*big.Int),
	}
}

// NewCacheStats creates a new cache statistics tracker
func NewCacheStats() *CacheStats {
	return &CacheStats{}
}

// Package-level cache instance
var sfcCache = NewSFCCache()

// GetSFCCache returns the SFC cache instance
func GetSFCCache() *SFCCache {
	return sfcCache
}

// Enhanced cache methods for Phase 1 optimizations

// GetValidatorData retrieves cached validator data or returns nil if not found
func (c *SFCCache) GetValidatorData(validatorID *big.Int) *CachedValidatorData {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	key := validatorID.String()
	if data, exists := c.ValidatorDataCache[key]; exists {
		data.AccessCount++
		c.Stats.ValidatorCacheHits++
		return data
	}

	c.Stats.ValidatorCacheMisses++
	return nil
}

// SetValidatorData stores validator data in cache
func (c *SFCCache) SetValidatorData(validatorID *big.Int, data *CachedValidatorData, blockNumber uint64) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	key := validatorID.String()
	data.LastUpdated = blockNumber
	c.ValidatorDataCache[key] = data
}

// GetEpochData retrieves cached epoch data or returns nil if not found
func (c *SFCCache) GetEpochData(epochNumber uint64) *CachedEpochData {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if data, exists := c.EpochDataCache[epochNumber]; exists {
		data.AccessCount++
		c.Stats.EpochCacheHits++
		return data
	}

	c.Stats.EpochCacheMisses++
	return nil
}

// SetEpochData stores epoch data in cache
func (c *SFCCache) SetEpochData(epochNumber uint64, data *CachedEpochData, blockNumber uint64) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	data.LastUpdated = blockNumber
	c.EpochDataCache[epochNumber] = data
}

// GetValidatorSlotFast calculates validator slots using enhanced caching
func (c *SFCCache) GetValidatorSlotFast(validatorID *big.Int, fieldType string) (*big.Int, uint64) {
	c.SlotCalculatorCache.mutex.RLock()
	cacheKey := validatorID.String() + "_" + fieldType
	if slot, exists := c.SlotCalculatorCache.CurrentEpochSlots[cacheKey]; exists {
		c.SlotCalculatorCache.mutex.RUnlock()
		c.Stats.SlotCacheHits++
		return slot, 0 // No gas for cache hit
	}
	c.SlotCalculatorCache.mutex.RUnlock()

	c.Stats.SlotCacheMisses++

	// Calculate slot
	var gasUsed uint64 = 0
	var slot *big.Int

	if offset, exists := c.SlotCalculatorCache.Offsets[fieldType]; exists {
		// Fast path for known validator fields
		hashInput := c.getCachedHashInput(validatorID, c.SlotCalculatorCache.ValidatorBaseSlot)
		hash := CachedKeccak256(hashInput)
		gasUsed += HashGasCost

		baseSlot := GetSlotBigInt().SetBytes(hash)
		slot = GetSlotBigInt().Add(baseSlot, GetSmallBigInt().SetInt64(offset))

		PutSlotBigInt(baseSlot)

		// Cache the result
		c.SlotCalculatorCache.mutex.Lock()
		c.SlotCalculatorCache.CurrentEpochSlots[cacheKey] = slot
		c.SlotCalculatorCache.mutex.Unlock()
	} else {
		// Fallback to original calculation for unknown fields
		switch fieldType {
		case "commission":
			slot, gasUsed = getValidatorCommissionSlot(validatorID)
		default:
			// Return error slot
			slot = GetSlotBigInt()
		}
	}

	return slot, gasUsed
}

// getCachedHashInput gets or creates cached hash input
func (c *SFCCache) getCachedHashInput(validatorID, baseSlot *big.Int) []byte {
	c.SlotCalculatorCache.mutex.RLock()
	cacheKey := validatorID.String() + "_" + baseSlot.String()
	if input, exists := c.SlotCalculatorCache.HashInputCache[cacheKey]; exists {
		c.SlotCalculatorCache.mutex.RUnlock()
		return input
	}
	c.SlotCalculatorCache.mutex.RUnlock()

	input := CreateHashInput(validatorID, baseSlot.Int64())

	c.SlotCalculatorCache.mutex.Lock()
	c.SlotCalculatorCache.HashInputCache[cacheKey] = input
	c.SlotCalculatorCache.mutex.Unlock()

	return input
}

// ClearEpochCache clears epoch-specific cache entries (call when epoch changes)
func (c *SFCCache) ClearEpochCache() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Clear current epoch slots
	c.SlotCalculatorCache.mutex.Lock()
	for k := range c.SlotCalculatorCache.CurrentEpochSlots {
		delete(c.SlotCalculatorCache.CurrentEpochSlots, k)
	}
	c.SlotCalculatorCache.mutex.Unlock()

	// Keep only recent epoch data (last 3 epochs)
	if len(c.EpochDataCache) > 3 {
		// Find the 3 most recent epochs
		var maxEpochs []uint64
		for epoch := range c.EpochDataCache {
			maxEpochs = append(maxEpochs, epoch)
		}

		// Sort and keep only the 3 most recent
		if len(maxEpochs) > 3 {
			// Simple selection of recent epochs (in a real implementation, you'd sort)
			for epoch := range c.EpochDataCache {
				if len(maxEpochs) > 3 {
					delete(c.EpochDataCache, epoch)
					maxEpochs = maxEpochs[1:] // Remove one element
				}
			}
		}
	}
}

// GetCacheStats returns current cache statistics
func (c *SFCCache) GetCacheStats() CacheStats {
	c.Stats.mutex.RLock()
	defer c.Stats.mutex.RUnlock()

	return CacheStats{
		HashCacheHits:        c.Stats.HashCacheHits,
		HashCacheMisses:      c.Stats.HashCacheMisses,
		SlotCacheHits:        c.Stats.SlotCacheHits,
		SlotCacheMisses:      c.Stats.SlotCacheMisses,
		ValidatorCacheHits:   c.Stats.ValidatorCacheHits,
		ValidatorCacheMisses: c.Stats.ValidatorCacheMisses,
		EpochCacheHits:       c.Stats.EpochCacheHits,
		EpochCacheMisses:     c.Stats.EpochCacheMisses,
		TotalOperations:      c.Stats.TotalOperations,
	}
}

// Helper functions for common operations

// CachedKeccak256 computes the Keccak256 hash using the cache
func CachedKeccak256(input []byte) []byte {
	return sfcCache.Hash.CachedKeccak256(input)
}

// CachedKeccak256Hash computes the Keccak256 hash using the cache
func CachedKeccak256Hash(input []byte) common.Hash {
	return sfcCache.Hash.CachedKeccak256Hash(input)
}

// GetCachedValidatorSlot gets or computes the validator slot
func GetCachedValidatorSlot(validatorID *big.Int) (*big.Int, uint64) {
	key := validatorID.String()

	if slot, found := sfcCache.ValidatorSlot[key]; found {
		return slot, 0 // No gas used for cache hit
	}

	// Compute if not found
	slot, gasUsed := getValidatorStatusSlot(validatorID)

	// Store in cache
	sfcCache.ValidatorSlot[key] = slot

	return slot, gasUsed
}

// GetCachedEpochSnapshotSlot gets or computes the epoch snapshot slot
func GetCachedEpochSnapshotSlot(epoch *big.Int) (*big.Int, uint64) {
	key := epoch.String()

	if slot, found := sfcCache.EpochSlot[key]; found {
		return slot, 0 // No gas used for cache hit
	}

	// Compute if not found
	slot, gasUsed := getEpochSnapshotSlot(epoch)

	// Store in cache
	sfcCache.EpochSlot[key] = slot

	return slot, gasUsed
}

// ClearCache clears all caches
func ClearCache() {
	sfcCache = NewSFCCache()
}

// CreateHashInput creates a hash input from a validator ID and slot constant
func CreateHashInput(validatorID *big.Int, slotConstant int64) []byte {
	// Create a cache key from validatorID and slotConstant
	cacheKey := validatorID.String() + "_" + strconv.FormatInt(slotConstant, 10)

	// Check if the hash input is already cached
	if hashInput, found := sfcCache.HashInputs[cacheKey]; found {
		return hashInput
	}

	// If not in cache, create the hash input directly
	validatorIDBytes := common.LeftPadBytes(validatorID.Bytes(), 32)
	slotBytes := common.LeftPadBytes(big.NewInt(slotConstant).Bytes(), 32)

	// Use the byte slice pool for the result
	hashInput := GetByteSlice()
	if cap(hashInput) < len(validatorIDBytes)+len(slotBytes) {
		// If the slice from the pool is too small, allocate a new one
		hashInput = make([]byte, 0, len(validatorIDBytes)+len(slotBytes))
	}

	// Combine the bytes
	hashInput = append(hashInput, validatorIDBytes...)
	hashInput = append(hashInput, slotBytes...)

	// Store in cache
	sfcCache.HashInputs[cacheKey] = hashInput

	return hashInput
}

// CreateAddressHashInput creates a hash input from an address and slot constant
func CreateAddressHashInput(addr common.Address, slotConstant int64) []byte {
	// Create a cache key from address and slotConstant
	cacheKey := addr.String() + "_" + strconv.FormatInt(slotConstant, 10)

	// Check if the hash input is already cached
	if hashInput, found := sfcCache.AddressHashInputs[cacheKey]; found {
		return hashInput
	}

	// If not in cache, create the hash input directly
	addrBytes := common.LeftPadBytes(addr.Bytes(), 32)
	slotBytes := common.LeftPadBytes(big.NewInt(slotConstant).Bytes(), 32)

	// Use the byte slice pool for the result
	hashInput := GetByteSlice()
	if cap(hashInput) < len(addrBytes)+len(slotBytes) {
		// If the slice from the pool is too small, allocate a new one
		hashInput = make([]byte, 0, len(addrBytes)+len(slotBytes))
	}

	// Combine the bytes
	hashInput = append(hashInput, addrBytes...)
	hashInput = append(hashInput, slotBytes...)

	// Store in cache
	sfcCache.AddressHashInputs[cacheKey] = hashInput

	return hashInput
}

// CreateNestedHashInput creates a hash input from a validator ID and a hash
func CreateNestedHashInput(validatorID *big.Int, hash []byte) []byte {
	validatorIDBytes := common.LeftPadBytes(validatorID.Bytes(), 32)

	// Use the byte slice pool for the result
	hashInput := GetByteSlice()
	if cap(hashInput) < len(validatorIDBytes)+len(hash) {
		// If the slice from the pool is too small, allocate a new one
		hashInput = make([]byte, 0, len(validatorIDBytes)+len(hash))
	}

	// Combine the bytes
	hashInput = append(hashInput, validatorIDBytes...)
	hashInput = append(hashInput, hash...)

	return hashInput
}

// CreateNestedAddressHashInput creates a hash input from an address and a hash
func CreateNestedAddressHashInput(addr common.Address, hash []byte) []byte {
	// Create a cache key from address and hash
	cacheKey := addr.String() + "_" + common.Bytes2Hex(hash)

	// Check if the hash input is already cached
	if hashInput, found := sfcCache.NestedHashInputs[cacheKey]; found {
		return hashInput
	}

	// If not in cache, create the hash input directly
	addrBytes := common.LeftPadBytes(addr.Bytes(), 32)

	// Use the byte slice pool for the result
	hashInput := GetByteSlice()
	if cap(hashInput) < len(addrBytes)+len(hash) {
		// If the slice from the pool is too small, allocate a new one
		hashInput = make([]byte, 0, len(addrBytes)+len(hash))
	}

	// Combine the bytes
	hashInput = append(hashInput, addrBytes...)
	hashInput = append(hashInput, hash...)

	// Store in cache
	sfcCache.NestedHashInputs[cacheKey] = hashInput

	return hashInput
}

// CreateValidatorMappingHashInput creates a hash input from a validator ID and a mapping slot
func CreateValidatorMappingHashInput(validatorID *big.Int, mappingSlot *big.Int) []byte {
	// Create a cache key from validatorID and mappingSlot
	cacheKey := validatorID.String() + "_mapping_" + mappingSlot.String()

	// Check if the hash input is already cached
	if hashInput, found := sfcCache.HashInputs[cacheKey]; found {
		return hashInput
	}

	// If not in cache, create the hash input directly
	validatorIDBytes := common.LeftPadBytes(validatorID.Bytes(), 32)
	mappingSlotBytes := common.LeftPadBytes(mappingSlot.Bytes(), 32)

	// Use the byte slice pool for the result
	hashInput := GetByteSlice()
	if cap(hashInput) < len(validatorIDBytes)+len(mappingSlotBytes) {
		// If the slice from the pool is too small, allocate a new one
		hashInput = make([]byte, 0, len(validatorIDBytes)+len(mappingSlotBytes))
	}

	// Combine the bytes
	hashInput = append(hashInput, validatorIDBytes...)
	hashInput = append(hashInput, mappingSlotBytes...)

	// Store in cache
	sfcCache.HashInputs[cacheKey] = hashInput

	return hashInput
}

// CreateAddressMethodHashInput creates a hash input from an address and a method ID
func CreateAddressMethodHashInput(addr common.Address, methodID []byte) []byte {
	// Create a cache key from address and methodID
	cacheKey := addr.String() + "_method_" + common.Bytes2Hex(methodID)

	// Check if the hash input is already cached
	if hashInput, found := sfcCache.AddressHashInputs[cacheKey]; found {
		return hashInput
	}

	// If not in cache, create the hash input directly
	addrBytes := common.LeftPadBytes(addr.Bytes(), 32)

	// Use the byte slice pool for the result
	hashInput := GetByteSlice()
	if cap(hashInput) < len(addrBytes)+len(methodID) {
		// If the slice from the pool is too small, allocate a new one
		hashInput = make([]byte, 0, len(addrBytes)+len(methodID))
	}

	// Combine the bytes
	hashInput = append(hashInput, addrBytes...)
	hashInput = append(hashInput, methodID...)

	// Store in cache
	sfcCache.AddressHashInputs[cacheKey] = hashInput

	return hashInput
}

// CreateAddressParamsHashInput creates a hash input from an address and multiple parameters
func CreateAddressParamsHashInput(addr common.Address, params ...[]byte) []byte {
	// Create a cache key from address and params
	cacheKey := addr.String()
	for _, param := range params {
		cacheKey += "_param_" + common.Bytes2Hex(param)
	}

	// Check if the hash input is already cached
	if hashInput, found := sfcCache.AddressHashInputs[cacheKey]; found {
		return hashInput
	}

	// If not in cache, create the hash input directly
	addrBytes := common.LeftPadBytes(addr.Bytes(), 32)

	// Calculate total length needed
	totalLength := len(addrBytes)
	for _, param := range params {
		totalLength += len(param)
	}

	// Use the byte slice pool for the result
	hashInput := GetByteSlice()
	if cap(hashInput) < totalLength {
		// If the slice from the pool is too small, allocate a new one
		hashInput = make([]byte, 0, totalLength)
	}

	// Combine the bytes
	hashInput = append(hashInput, addrBytes...)
	for _, param := range params {
		hashInput = append(hashInput, param...)
	}

	// Store in cache
	sfcCache.AddressHashInputs[cacheKey] = hashInput

	return hashInput
}

// CreateOffsetSlotHashInput creates a hash input from an offset and a slot
func CreateOffsetSlotHashInput(offset int64, slot *big.Int) []byte {
	// Create a cache key from offset and slot
	cacheKey := strconv.FormatInt(offset, 10) + "_slot_" + slot.String()

	// Check if the hash input is already cached
	if hashInput, found := sfcCache.HashInputs[cacheKey]; found {
		return hashInput
	}

	// If not in cache, create the hash input directly
	offsetBytes := common.LeftPadBytes(big.NewInt(offset).Bytes(), 32)
	slotBytes := common.LeftPadBytes(slot.Bytes(), 32)

	// Use the byte slice pool for the result
	hashInput := GetByteSlice()
	if cap(hashInput) < len(offsetBytes)+len(slotBytes) {
		// If the slice from the pool is too small, allocate a new one
		hashInput = make([]byte, 0, len(offsetBytes)+len(slotBytes))
	}

	// Combine the bytes
	hashInput = append(hashInput, offsetBytes...)
	hashInput = append(hashInput, slotBytes...)

	// Store in cache
	sfcCache.HashInputs[cacheKey] = hashInput

	return hashInput
}

// CreateAndHashOffsetSlot creates a hash input from an offset and a slot, then hashes it
func CreateAndHashOffsetSlot(offset int64, slot *big.Int) []byte {
	// Get the hash input
	hashInput := CreateOffsetSlotHashInput(offset, slot)

	// Use cached hash calculation
	return CachedKeccak256(hashInput)
}

// ABI type constants for identifying which ABI to use
const (
	SfcAbiType            = "sfc"
	CMAbiType             = "cm"
	NodeDriverAbiType     = "nodedriver"
	NodeDriverAuthAbiType = "nodedriverauth"
)

// CachedAbiPack packs arguments using the specified ABI and caches the result
// Only caches results for calls without parameters to avoid cache bloat
func CachedAbiPack(abiType, method string, args ...interface{}) ([]byte, error) {
	// Only cache if there are no arguments
	if len(args) == 0 {
		// Create a cache key from just the ABI type and method
		key := abiType + ":" + method

		// Check if the result is already cached
		if packed, ok := sfcCache.AbiPackCache[key]; ok {
			return packed, nil
		}

		// Not in cache, pack it
		var packed []byte
		var err error

		switch abiType {
		case SfcAbiType:
			packed, err = SfcAbi.Methods[method].Outputs.Pack()
		case CMAbiType:
			packed, err = CMAbi.Methods[method].Outputs.Pack()
		case NodeDriverAbiType:
			packed, err = NodeDriverAbi.Methods[method].Outputs.Pack()
		case NodeDriverAuthAbiType:
			packed, err = NodeDriverAuthAbi.Methods[method].Outputs.Pack()
		default:
			return nil, fmt.Errorf("unknown ABI type: %s", abiType)
		}

		if err != nil {
			return nil, err
		}

		// Store in cache
		sfcCache.AbiPackCache[key] = packed

		return packed, nil
	}

	// For calls with parameters, don't use cache
	var packed []byte
	var err error

	switch abiType {
	case SfcAbiType:
		packed, err = SfcAbi.Methods[method].Outputs.Pack(args...)
	case CMAbiType:
		packed, err = CMAbi.Methods[method].Outputs.Pack(args...)
	case NodeDriverAbiType:
		packed, err = NodeDriverAbi.Methods[method].Outputs.Pack(args...)
	case NodeDriverAuthAbiType:
		packed, err = NodeDriverAuthAbi.Methods[method].Outputs.Pack(args...)
	default:
		return nil, fmt.Errorf("unknown ABI type: %s", abiType)
	}

	return packed, err
}
