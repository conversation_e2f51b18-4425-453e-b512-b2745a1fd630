// Package sfc implements the SFC (Special Fee Contract) precompiled contract.
package sfc

import (
	"math/big"
	"sync"

	"github.com/unicornultrafoundation/go-u2u/common"
)

// Specialized BigInt pools for different use cases
var (
	// SmallBigIntPool for values that typically fit in 64 bits (validator IDs, epochs, etc.)
	SmallBigIntPool = sync.Pool{
		New: func() interface{} {
			return new(big.Int)
		},
	}

	// LargeBigIntPool for values that may be very large (stakes, rewards, etc.)
	LargeBigIntPool = sync.Pool{
		New: func() interface{} {
			return new(big.Int)
		},
	}

	// SlotBigIntPool specifically for storage slot calculations
	SlotBigIntPool = sync.Pool{
		New: func() interface{} {
			return new(big.Int)
		},
	}

	// Legacy pool for backward compatibility
	BigIntPool = SmallBigIntPool
)

// Specialized byte slice pools
var (
	// ByteSlicePool for general purpose byte slices (32 bytes)
	ByteSlicePool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 32)
		},
	}

	// HashInputPool for hash input byte slices (64 bytes typical)
	HashInputPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 64)
		},
	}

	// LargeByteSlicePool for larger byte operations (128 bytes)
	LargeByteSlicePool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 128)
		},
	}
)

// Specialized slice pools for common operations
var (
	// ValidatorIDSlicePool for validator ID slices
	ValidatorIDSlicePool = sync.Pool{
		New: func() interface{} {
			return make([]*big.Int, 0, 100) // Pre-allocate for typical validator count
		},
	}

	// HashSlicePool for hash slices
	HashSlicePool = sync.Pool{
		New: func() interface{} {
			return make([]common.Hash, 0, 100)
		},
	}

	// UptimeSlicePool for uptime slices
	UptimeSlicePool = sync.Pool{
		New: func() interface{} {
			return make([]*big.Int, 0, 100)
		},
	}
)

// Specialized BigInt pool functions
func GetSmallBigInt() *big.Int {
	return SmallBigIntPool.Get().(*big.Int).SetInt64(0)
}

func PutSmallBigInt(b *big.Int) {
	if b != nil {
		SmallBigIntPool.Put(b)
	}
}

func GetLargeBigInt() *big.Int {
	return LargeBigIntPool.Get().(*big.Int).SetInt64(0)
}

func PutLargeBigInt(b *big.Int) {
	if b != nil {
		LargeBigIntPool.Put(b)
	}
}

func GetSlotBigInt() *big.Int {
	return SlotBigIntPool.Get().(*big.Int).SetInt64(0)
}

func PutSlotBigInt(b *big.Int) {
	if b != nil {
		SlotBigIntPool.Put(b)
	}
}

// Legacy functions for backward compatibility
func GetBigInt() *big.Int {
	return GetSmallBigInt()
}

func PutBigInt(b *big.Int) {
	PutSmallBigInt(b)
}

// Specialized byte slice pool functions
func GetByteSlice() []byte {
	return ByteSlicePool.Get().([]byte)[:0]
}

func PutByteSlice(b []byte) {
	if cap(b) >= 32 {
		ByteSlicePool.Put(b[:0])
	}
}

func GetHashInput() []byte {
	return HashInputPool.Get().([]byte)[:0]
}

func PutHashInput(b []byte) {
	if cap(b) >= 64 {
		HashInputPool.Put(b[:0])
	}
}

func GetLargeByteSlice() []byte {
	return LargeByteSlicePool.Get().([]byte)[:0]
}

func PutLargeByteSlice(b []byte) {
	if cap(b) >= 128 {
		LargeByteSlicePool.Put(b[:0])
	}
}

// GetPaddedBytes gets a byte slice from the pool and pads it
func GetPaddedBytes(data []byte, length int) []byte {
	result := GetByteSlice()
	if cap(result) < length {
		// If the slice from the pool is too small, allocate a new one
		result = make([]byte, length)
	} else {
		// Otherwise, resize the slice from the pool
		result = result[:length]
	}

	// Perform the padding (right-aligned)
	copy(result[length-len(data):], data)
	return result
}

// GetLeftPaddedBytes gets a byte slice from the pool and left-pads it
// This is a replacement for common.LeftPadBytes that uses the pool
func GetLeftPaddedBytes(data []byte, length int) []byte {
	result := GetPaddedBytes(data, length)
	return result
}

// BigIntToBytes converts a big.Int to a byte slice using the pool
func BigIntToBytes(b *big.Int) []byte {
	if b == nil {
		return GetByteSlice()
	}
	return GetLeftPaddedBytes(b.Bytes(), 32)
}

// AddressToPaddedBytes converts an address to a padded byte slice using the pool
func AddressToPaddedBytes(addr common.Address) []byte {
	return GetLeftPaddedBytes(addr.Bytes(), 32)
}

// Specialized slice pool functions
func GetValidatorIDSlice() []*big.Int {
	return ValidatorIDSlicePool.Get().([]*big.Int)[:0]
}

func PutValidatorIDSlice(slice []*big.Int) {
	if cap(slice) >= 50 { // Only return reasonably sized slices
		ValidatorIDSlicePool.Put(slice[:0])
	}
}

func GetHashSlice() []common.Hash {
	return HashSlicePool.Get().([]common.Hash)[:0]
}

func PutHashSlice(slice []common.Hash) {
	if cap(slice) >= 50 {
		HashSlicePool.Put(slice[:0])
	}
}

func GetUptimeSlice() []*big.Int {
	return UptimeSlicePool.Get().([]*big.Int)[:0]
}

func PutUptimeSlice(slice []*big.Int) {
	if cap(slice) >= 50 {
		UptimeSlicePool.Put(slice[:0])
	}
}

// Pool statistics for monitoring
type PoolStats struct {
	SmallBigIntPoolSize    int
	LargeBigIntPoolSize    int
	SlotBigIntPoolSize     int
	ByteSlicePoolSize      int
	HashInputPoolSize      int
	LargeByteSlicePoolSize int
	ValidatorIDSliceSize   int
	HashSliceSize          int
	UptimeSliceSize        int
}

// GetPoolStats returns current pool statistics (for monitoring)
func GetPoolStats() PoolStats {
	// Note: sync.Pool doesn't expose size, so this is a placeholder
	// In a real implementation, you might want to add custom counters
	return PoolStats{
		// These would need custom tracking if detailed stats are needed
		SmallBigIntPoolSize:    0,
		LargeBigIntPoolSize:    0,
		SlotBigIntPoolSize:     0,
		ByteSlicePoolSize:      0,
		HashInputPoolSize:      0,
		LargeByteSlicePoolSize: 0,
		ValidatorIDSliceSize:   0,
		HashSliceSize:          0,
		UptimeSliceSize:        0,
	}
}
