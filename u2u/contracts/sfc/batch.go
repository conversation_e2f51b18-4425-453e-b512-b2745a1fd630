package sfc

import (
	"math/big"
	"sync"

	"github.com/unicornultrafoundation/go-u2u/common"
	"github.com/unicornultrafoundation/go-u2u/core/vm"
)

// StateBatch provides batched operations for StateDB to reduce individual calls
type StateBatch struct {
	reads     map[common.Hash]bool
	writes    map[common.Hash]common.Hash
	readCache map[common.Hash]common.Hash
	gasUsed   uint64
	mutex     sync.RWMutex
}

// NewStateBatch creates a new state batch
func NewStateBatch() *StateBatch {
	return &StateBatch{
		reads:     make(map[common.Hash]bool),
		writes:    make(map[common.Hash]common.Hash),
		readCache: make(map[common.Hash]common.Hash),
		gasUsed:   0,
	}
}

// BatchRead reads multiple slots from StateDB in a single operation
func (b *StateBatch) BatchRead(evm *vm.EVM, slots []common.Hash) map[common.Hash]common.Hash {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	results := make(map[common.Hash]common.Hash, len(slots))

	for _, slot := range slots {
		// Check if already read in this batch
		if value, exists := b.readCache[slot]; exists {
			results[slot] = value
			continue
		}

		// Read from StateDB
		value := evm.SfcStateDB.GetState(ContractAddress, slot)
		b.readCache[slot] = value
		b.reads[slot] = true
		b.gasUsed += SloadGasCost
		results[slot] = value
	}

	return results
}

// GetState gets a single state value, using cache if available
func (b *StateBatch) GetState(evm *vm.EVM, slot common.Hash) common.Hash {
	b.mutex.RLock()
	if value, exists := b.readCache[slot]; exists {
		b.mutex.RUnlock()
		return value
	}
	b.mutex.RUnlock()

	b.mutex.Lock()
	defer b.mutex.Unlock()

	// Double-check after acquiring write lock
	if value, exists := b.readCache[slot]; exists {
		return value
	}

	value := evm.SfcStateDB.GetState(ContractAddress, slot)
	b.readCache[slot] = value
	b.reads[slot] = true
	b.gasUsed += SloadGasCost

	return value
}

// SetState queues a state write for batching
func (b *StateBatch) SetState(slot common.Hash, value common.Hash) {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	b.writes[slot] = value
	// Update read cache to reflect pending write
	b.readCache[slot] = value
}

// BatchWrite writes all queued state changes to StateDB
func (b *StateBatch) BatchWrite(evm *vm.EVM) uint64 {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	for slot, value := range b.writes {
		evm.SfcStateDB.SetState(ContractAddress, slot, value)
		b.gasUsed += SstoreGasCost
	}

	return b.gasUsed
}

// Flush writes all pending changes and returns total gas used
func (b *StateBatch) Flush(evm *vm.EVM) uint64 {
	totalGas := b.BatchWrite(evm)
	b.Clear()
	return totalGas
}

// Clear resets the batch for reuse
func (b *StateBatch) Clear() {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	// Clear maps but keep allocated memory
	for k := range b.reads {
		delete(b.reads, k)
	}
	for k := range b.writes {
		delete(b.writes, k)
	}
	for k := range b.readCache {
		delete(b.readCache, k)
	}

	b.gasUsed = 0
}

// GetGasUsed returns the total gas used by this batch
func (b *StateBatch) GetGasUsed() uint64 {
	b.mutex.RLock()
	defer b.mutex.RUnlock()
	return b.gasUsed
}

// HasPendingWrites returns true if there are pending writes
func (b *StateBatch) HasPendingWrites() bool {
	b.mutex.RLock()
	defer b.mutex.RUnlock()
	return len(b.writes) > 0
}

// GetPendingWriteCount returns the number of pending writes
func (b *StateBatch) GetPendingWriteCount() int {
	b.mutex.RLock()
	defer b.mutex.RUnlock()
	return len(b.writes)
}

// GetReadCount returns the number of reads performed
func (b *StateBatch) GetReadCount() int {
	b.mutex.RLock()
	defer b.mutex.RUnlock()
	return len(b.reads)
}

// BatchOperations provides high-level batch operations for common patterns
type BatchOperations struct {
	batch *StateBatch
}

// NewBatchOperations creates a new batch operations helper
func NewBatchOperations() *BatchOperations {
	return &BatchOperations{
		batch: NewStateBatch(),
	}
}

// ReadValidatorData reads all validator-related data in a single batch
func (bo *BatchOperations) ReadValidatorData(evm *vm.EVM, validatorIDs []*big.Int) (map[string]map[string]common.Hash, uint64) {
	// Pre-calculate all slots
	slots := GetHashSlice()
	defer PutHashSlice(slots)

	for _, validatorID := range validatorIDs {
		// Calculate validator slots
		statusSlot, _ := getValidatorStatusSlot(validatorID)
		commissionSlot, _ := getValidatorCommissionSlot(validatorID)
		receivedStakeSlot, _ := getValidatorReceivedStakeSlot(validatorID)
		createdEpochSlot, _ := getValidatorCreatedEpochSlot(validatorID)
		createdTimeSlot, _ := getValidatorCreatedTimeSlot(validatorID)
		deactivatedTimeSlot, _ := getValidatorDeactivatedTimeSlot(validatorID)
		deactivatedEpochSlot, _ := getValidatorDeactivatedEpochSlot(validatorID)
		authSlot, _ := getValidatorAuthSlot(validatorID)

		slots = append(slots,
			common.BigToHash(statusSlot),
			common.BigToHash(commissionSlot),
			common.BigToHash(receivedStakeSlot),
			common.BigToHash(createdEpochSlot),
			common.BigToHash(createdTimeSlot),
			common.BigToHash(deactivatedTimeSlot),
			common.BigToHash(deactivatedEpochSlot),
			common.BigToHash(authSlot),
		)
	}

	// Batch read all slots
	results := bo.batch.BatchRead(evm, slots)

	// Organize results by validator
	validatorData := make(map[string]map[string]common.Hash)
	slotIndex := 0

	for _, validatorID := range validatorIDs {
		validatorKey := validatorID.String()
		validatorData[validatorKey] = map[string]common.Hash{
			"status":           results[slots[slotIndex]],
			"commission":       results[slots[slotIndex+1]],
			"receivedStake":    results[slots[slotIndex+2]],
			"createdEpoch":     results[slots[slotIndex+3]],
			"createdTime":      results[slots[slotIndex+4]],
			"deactivatedTime":  results[slots[slotIndex+5]],
			"deactivatedEpoch": results[slots[slotIndex+6]],
			"auth":             results[slots[slotIndex+7]],
		}
		slotIndex += 8
	}

	return validatorData, bo.batch.GetGasUsed()
}

// WriteValidatorData writes validator data in a batch
func (bo *BatchOperations) WriteValidatorData(evm *vm.EVM, validatorID *big.Int, data map[string]common.Hash) uint64 {
	// Calculate slots and set values
	if status, exists := data["status"]; exists {
		statusSlot, _ := getValidatorStatusSlot(validatorID)
		bo.batch.SetState(common.BigToHash(statusSlot), status)
	}

	if commission, exists := data["commission"]; exists {
		commissionSlot, _ := getValidatorCommissionSlot(validatorID)
		bo.batch.SetState(common.BigToHash(commissionSlot), commission)
	}

	if receivedStake, exists := data["receivedStake"]; exists {
		receivedStakeSlot, _ := getValidatorReceivedStakeSlot(validatorID)
		bo.batch.SetState(common.BigToHash(receivedStakeSlot), receivedStake)
	}

	if deactivatedTime, exists := data["deactivatedTime"]; exists {
		deactivatedTimeSlot, _ := getValidatorDeactivatedTimeSlot(validatorID)
		bo.batch.SetState(common.BigToHash(deactivatedTimeSlot), deactivatedTime)
	}

	if createdEpoch, exists := data["createdEpoch"]; exists {
		createdEpochSlot, _ := getValidatorCreatedEpochSlot(validatorID)
		bo.batch.SetState(common.BigToHash(createdEpochSlot), createdEpoch)
	}

	if createdTime, exists := data["createdTime"]; exists {
		createdTimeSlot, _ := getValidatorCreatedTimeSlot(validatorID)
		bo.batch.SetState(common.BigToHash(createdTimeSlot), createdTime)
	}

	if deactivatedEpoch, exists := data["deactivatedEpoch"]; exists {
		deactivatedEpochSlot, _ := getValidatorDeactivatedEpochSlot(validatorID)
		bo.batch.SetState(common.BigToHash(deactivatedEpochSlot), deactivatedEpoch)
	}

	if auth, exists := data["auth"]; exists {
		authSlot, _ := getValidatorAuthSlot(validatorID)
		bo.batch.SetState(common.BigToHash(authSlot), auth)
	}

	if pubkey, exists := data["pubkey"]; exists {
		pubkeySlot, _ := getValidatorPubkeySlot(validatorID)
		bo.batch.SetState(common.BigToHash(pubkeySlot), pubkey)
	}

	return bo.batch.BatchWrite(evm)
}

// ReadEpochData reads epoch snapshot data in a batch
func (bo *BatchOperations) ReadEpochData(evm *vm.EVM, epoch *big.Int, validatorIDs []*big.Int) (map[common.Hash]common.Hash, uint64) {
	epochSnapshotSlot, _ := getEpochSnapshotSlot(epoch)

	slots := GetHashSlice()
	defer PutHashSlice(slots)

	// Add epoch-level slots
	totalStakeSlot := GetSlotBigInt()
	totalStakeSlot.Add(epochSnapshotSlot, GetSmallBigInt().SetInt64(totalStakeOffset))
	slots = append(slots, common.BigToHash(totalStakeSlot))
	PutSlotBigInt(totalStakeSlot)

	totalBaseRewardSlot := GetSlotBigInt()
	totalBaseRewardSlot.Add(epochSnapshotSlot, GetSmallBigInt().SetInt64(totalBaseRewardOffset))
	slots = append(slots, common.BigToHash(totalBaseRewardSlot))
	PutSlotBigInt(totalBaseRewardSlot)

	totalTxRewardSlot := GetSlotBigInt()
	totalTxRewardSlot.Add(epochSnapshotSlot, GetSmallBigInt().SetInt64(totalTxRewardOffset))
	slots = append(slots, common.BigToHash(totalTxRewardSlot))
	PutSlotBigInt(totalTxRewardSlot)

	// Add validator-specific epoch slots
	for _, validatorID := range validatorIDs {
		accumulatedRewardSlot, _ := getEpochValidatorAccumulatedRewardPerTokenSlot(epoch, validatorID)
		accumulatedUptimeSlot, _ := getEpochValidatorAccumulatedUptimeSlot(epoch, validatorID)
		accumulatedTxsFeeSlot, _ := getEpochValidatorAccumulatedOriginatedTxsFeeSlot(epoch, validatorID)
		offlineTimeSlot, _ := getEpochValidatorOfflineTimeSlot(epoch, validatorID)
		offlineBlocksSlot, _ := getEpochValidatorOfflineBlocksSlot(epoch, validatorID)

		slots = append(slots,
			common.BigToHash(accumulatedRewardSlot),
			common.BigToHash(accumulatedUptimeSlot),
			common.BigToHash(accumulatedTxsFeeSlot),
			common.BigToHash(offlineTimeSlot),
			common.BigToHash(offlineBlocksSlot),
		)
	}

	// Batch read all slots
	results := bo.batch.BatchRead(evm, slots)

	return results, bo.batch.GetGasUsed()
}

// Flush flushes all pending operations
func (bo *BatchOperations) Flush(evm *vm.EVM) uint64 {
	return bo.batch.BatchWrite(evm)
}

// Clear clears the batch for reuse
func (bo *BatchOperations) Clear() {
	bo.batch.Clear()
}
