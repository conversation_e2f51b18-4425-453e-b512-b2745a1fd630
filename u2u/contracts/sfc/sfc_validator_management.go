// Package sfc implements the SFC (Special Fee Contract) precompiled contract.
package sfc

import (
	"math/big"
	"time"

	"github.com/unicornultrafoundation/go-u2u/common"
	"github.com/unicornultrafoundation/go-u2u/core/vm"
)

// OptimizedValidatorUpdate demonstrates complex validator state transitions using batch operations with GetState
func OptimizedValidatorUpdate(evm *vm.EVM, validatorID *big.Int, updates map[string]*big.Int) (uint64, error) {
	batch := NewStateBatch()
	defer batch.Clear()

	var gasUsed uint64 = 0

	// Get validator status slot for conditional logic
	statusSlot, slotGas := getValidatorStatusSlot(validatorID)
	gasUsed += slotGas

	// Read current status using GetState (includes any pending writes)
	currentStatus := batch.GetState(evm, common.BigToHash(statusSlot))
	statusBigInt := GetSmallBigInt().SetBytes(currentStatus.Bytes())
	defer PutSmallBigInt(statusBigInt)

	// Process updates based on current state
	if newStatus, exists := updates["status"]; exists {
		// If changing status, handle related state changes
		if statusBigInt.Cmp(GetSmallBigInt().SetInt64(0)) == 0 && newStatus.Cmp(GetSmallBigInt().SetInt64(1)) == 0 {
			// Validator being deactivated - set deactivation time
			deactivatedTimeSlot, slotGas := getValidatorDeactivatedTimeSlot(validatorID)
			gasUsed += slotGas

			currentTime := GetSmallBigInt().SetInt64(time.Now().Unix())
			batch.SetState(common.BigToHash(deactivatedTimeSlot), common.BigToHash(currentTime))
			PutSmallBigInt(currentTime)
		}

		// Update status
		batch.SetState(common.BigToHash(statusSlot), common.BigToHash(newStatus))
	}

	// Handle stake updates with validation
	if newStake, exists := updates["receivedStake"]; exists {
		receivedStakeSlot, slotGas := getValidatorReceivedStakeSlot(validatorID)
		gasUsed += slotGas

		// Read current stake
		currentStake := batch.GetState(evm, common.BigToHash(receivedStakeSlot))
		currentStakeBigInt := GetLargeBigInt().SetBytes(currentStake.Bytes())

		// Calculate stake difference for total stake adjustment
		stakeDiff := GetLargeBigInt().Sub(newStake, currentStakeBigInt)

		// Update validator stake
		batch.SetState(common.BigToHash(receivedStakeSlot), common.BigToHash(newStake))

		// Update total stake accordingly
		totalStakeSlotHash := common.BigToHash(GetSmallBigInt().SetInt64(totalStakeSlot))
		currentTotalStake := batch.GetState(evm, totalStakeSlotHash)
		totalStakeBigInt := GetLargeBigInt().SetBytes(currentTotalStake.Bytes())
		newTotalStake := GetLargeBigInt().Add(totalStakeBigInt, stakeDiff)

		batch.SetState(totalStakeSlotHash, common.BigToHash(newTotalStake))

		// Clean up
		PutLargeBigInt(currentStakeBigInt)
		PutLargeBigInt(stakeDiff)
		PutLargeBigInt(totalStakeBigInt)
		PutLargeBigInt(newTotalStake)
	}

	// Handle commission updates
	if newCommission, exists := updates["commission"]; exists {
		commissionSlot, slotGas := getValidatorCommissionSlot(validatorID)
		gasUsed += slotGas

		batch.SetState(common.BigToHash(commissionSlot), common.BigToHash(newCommission))
	}

	// Execute all batched updates
	batchGasUsed := batch.BatchWrite(evm)
	gasUsed += batchGasUsed

	return gasUsed, nil
}

// OptimizedDelegationWithRewards demonstrates delegation with automatic reward stashing using GetState
func OptimizedDelegationWithRewards(evm *vm.EVM, delegator common.Address, validatorID *big.Int, amount *big.Int) (uint64, error) {
	batch := NewStateBatch()
	defer batch.Clear()

	var gasUsed uint64 = 0

	// Get delegation slot
	delegationSlot, slotGas := getStakeSlot(delegator, validatorID)
	gasUsed += slotGas

	// Read current delegation using GetState
	currentDelegation := batch.GetState(evm, common.BigToHash(delegationSlot))
	currentDelegationBigInt := GetLargeBigInt().SetBytes(currentDelegation.Bytes())
	defer PutLargeBigInt(currentDelegationBigInt)

	// Check if this is a new delegation (current amount is 0)
	isNewDelegation := currentDelegationBigInt.Cmp(GetSmallBigInt().SetInt64(0)) == 0

	if !isNewDelegation {
		// Existing delegation - need to stash rewards first
		rewardsSlot, slotGas := getRewardsStashSlot(delegator, validatorID)
		gasUsed += slotGas

		// Read current stashed rewards
		currentRewards := batch.GetState(evm, common.BigToHash(rewardsSlot))
		currentRewardsBigInt := GetLargeBigInt().SetBytes(currentRewards.Bytes())

		// Calculate pending rewards (simplified calculation for demo)
		pendingRewards := GetLargeBigInt().Mul(currentDelegationBigInt, GetSmallBigInt().SetInt64(100)) // 100 wei per unit
		newTotalRewards := GetLargeBigInt().Add(currentRewardsBigInt, pendingRewards)

		// Update stashed rewards
		batch.SetState(common.BigToHash(rewardsSlot), common.BigToHash(newTotalRewards))

		// Clean up
		PutLargeBigInt(currentRewardsBigInt)
		PutLargeBigInt(pendingRewards)
		PutLargeBigInt(newTotalRewards)
	}

	// Update delegation amount
	newDelegation := GetLargeBigInt().Add(currentDelegationBigInt, amount)
	batch.SetState(common.BigToHash(delegationSlot), common.BigToHash(newDelegation))

	// Update validator's received stake
	validatorStakeSlot, slotGas := getValidatorReceivedStakeSlot(validatorID)
	gasUsed += slotGas

	currentValidatorStake := batch.GetState(evm, common.BigToHash(validatorStakeSlot))
	validatorStakeBigInt := GetLargeBigInt().SetBytes(currentValidatorStake.Bytes())
	newValidatorStake := GetLargeBigInt().Add(validatorStakeBigInt, amount)

	batch.SetState(common.BigToHash(validatorStakeSlot), common.BigToHash(newValidatorStake))

	// Clean up
	PutLargeBigInt(newDelegation)
	PutLargeBigInt(validatorStakeBigInt)
	PutLargeBigInt(newValidatorStake)

	// Execute all batched updates
	batchGasUsed := batch.BatchWrite(evm)
	gasUsed += batchGasUsed

	return gasUsed, nil
}

// OptimizedEpochTransition demonstrates epoch transition logic using GetState for complex state management
func OptimizedEpochTransition(evm *vm.EVM, newEpoch *big.Int, validatorIDs []*big.Int, rewards []*big.Int) (uint64, error) {
	batch := NewStateBatch()
	defer batch.Clear()

	var gasUsed uint64 = 0

	// Update current sealed epoch
	currentSealedEpochSlotHash := common.BigToHash(GetSmallBigInt().SetInt64(currentSealedEpochSlot))
	batch.SetState(currentSealedEpochSlotHash, common.BigToHash(newEpoch))

	// Process each validator's epoch transition
	for i, validatorID := range validatorIDs {
		// Get validator status to check if active
		statusSlot, slotGas := getValidatorStatusSlot(validatorID)
		gasUsed += slotGas

		validatorStatus := batch.GetState(evm, common.BigToHash(statusSlot))
		statusBigInt := GetSmallBigInt().SetBytes(validatorStatus.Bytes())

		// Only process rewards for active validators
		if statusBigInt.Cmp(GetSmallBigInt().SetInt64(0)) == 0 { // OK_STATUS
			// Update validator's accumulated rewards
			rewardSlot, slotGas := getEpochValidatorAccumulatedRewardPerTokenSlot(newEpoch, validatorID)
			gasUsed += slotGas

			// Read current accumulated rewards for this epoch
			currentRewards := batch.GetState(evm, common.BigToHash(rewardSlot))
			currentRewardsBigInt := GetLargeBigInt().SetBytes(currentRewards.Bytes())

			// Add new rewards
			newRewards := GetLargeBigInt().Add(currentRewardsBigInt, rewards[i])
			batch.SetState(common.BigToHash(rewardSlot), common.BigToHash(newRewards))

			// Update validator's total received stake if needed
			stakeSlot, slotGas := getValidatorReceivedStakeSlot(validatorID)
			gasUsed += slotGas

			currentStake := batch.GetState(evm, common.BigToHash(stakeSlot))
			stakeBigInt := GetLargeBigInt().SetBytes(currentStake.Bytes())

			// Apply any stake adjustments based on performance
			if rewards[i].Cmp(GetSmallBigInt().SetInt64(1000)) > 0 { // High performance bonus
				bonus := GetLargeBigInt().Div(rewards[i], GetSmallBigInt().SetInt64(100)) // 1% bonus
				newStake := GetLargeBigInt().Add(stakeBigInt, bonus)
				batch.SetState(common.BigToHash(stakeSlot), common.BigToHash(newStake))
				PutLargeBigInt(bonus)
				PutLargeBigInt(newStake)
			}

			// Clean up
			PutLargeBigInt(currentRewardsBigInt)
			PutLargeBigInt(newRewards)
			PutLargeBigInt(stakeBigInt)
		}

		PutSmallBigInt(statusBigInt)
	}

	// Execute all batched updates
	batchGasUsed := batch.BatchWrite(evm)
	gasUsed += batchGasUsed

	return gasUsed, nil
}

// OptimizedValidatorStatusCheck demonstrates reading validator state with pending changes
func OptimizedValidatorStatusCheck(evm *vm.EVM, validatorID *big.Int) (map[string]*big.Int, uint64, error) {
	batch := NewStateBatch()
	defer batch.Clear()

	var gasUsed uint64 = 0
	result := make(map[string]*big.Int)

	// Read all validator data using GetState (includes pending writes)
	statusSlot, slotGas := getValidatorStatusSlot(validatorID)
	gasUsed += slotGas
	status := batch.GetState(evm, common.BigToHash(statusSlot))
	result["status"] = GetSmallBigInt().SetBytes(status.Bytes())

	stakeSlot, slotGas := getValidatorReceivedStakeSlot(validatorID)
	gasUsed += slotGas
	stake := batch.GetState(evm, common.BigToHash(stakeSlot))
	result["receivedStake"] = GetLargeBigInt().SetBytes(stake.Bytes())

	commissionSlot, slotGas := getValidatorCommissionSlot(validatorID)
	gasUsed += slotGas
	commission := batch.GetState(evm, common.BigToHash(commissionSlot))
	result["commission"] = GetSmallBigInt().SetBytes(commission.Bytes())

	createdTimeSlot, slotGas := getValidatorCreatedTimeSlot(validatorID)
	gasUsed += slotGas
	createdTime := batch.GetState(evm, common.BigToHash(createdTimeSlot))
	result["createdTime"] = GetSmallBigInt().SetBytes(createdTime.Bytes())

	deactivatedTimeSlot, slotGas := getValidatorDeactivatedTimeSlot(validatorID)
	gasUsed += slotGas
	deactivatedTime := batch.GetState(evm, common.BigToHash(deactivatedTimeSlot))
	result["deactivatedTime"] = GetSmallBigInt().SetBytes(deactivatedTime.Bytes())

	// Add gas used for reads
	gasUsed += batch.GetGasUsed()

	return result, gasUsed, nil
}
