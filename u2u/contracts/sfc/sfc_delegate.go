package sfc

import (
	"math/big"

	"github.com/unicornultrafoundation/go-u2u/common"
	"github.com/unicornultrafoundation/go-u2u/core/types"
	"github.com/unicornultrafoundation/go-u2u/core/vm"
	"github.com/unicornultrafoundation/go-u2u/params"
)

// handleDelegate delegates stake to a validator
func handleDelegate(evm *vm.EVM, caller common.Address, args []interface{}, value *big.Int) ([]byte, uint64, error) {
	// Initialize gas used
	var gasUsed uint64 = 0
	// Get the arguments
	if len(args) != 1 {
		return nil, 0, vm.ErrExecutionReverted
	}
	toValidatorID, ok := args[0].(*big.Int)
	if !ok {
		return nil, 0, vm.ErrExecutionReverted
	}

	// Call the internal _delegate function
	result, delegateGasUsed, err := handleInternalDelegate(evm, caller, toValidatorID, value)
	if err != nil {
		return result, gasUsed + delegateGasUsed, err
	}

	// Add the gas used by handleInternalDelegate
	gasUsed += delegateGasUsed

	// Emit Delegated event
	topics := []common.Hash{
		SfcAbi.Events["Delegated"].ID,
		common.BytesToHash(common.LeftPadBytes(caller.Bytes(), 32)), // indexed parameter (delegator)
		common.BigToHash(toValidatorID),                             // indexed parameter (toValidatorID)
	}
	data, err := SfcLibAbi.Events["Delegated"].Inputs.NonIndexed().Pack(
		value,
	)
	if err != nil {
		return nil, 0, vm.ErrExecutionReverted
	}
	evm.SfcStateDB.AddLog(&types.Log{
		Address: ContractAddress,
		Topics:  topics,
		Data:    data,
	})

	// Recount votes
	// Get the validator auth address
	validatorAuthSlot, _ := getValidatorAuthSlot(toValidatorID)
	validatorAuth := evm.SfcStateDB.GetState(ContractAddress, common.BigToHash(validatorAuthSlot))
	validatorAuthAddr := common.BytesToAddress(validatorAuth.Bytes())

	// Call handleRecountVotes with strict=false
	result, recountGasUsed, err := handleRecountVotes(evm, caller, validatorAuthAddr, false)
	if err != nil {
		// We don't return an error here because recountVotes is not critical
		// and we don't want to revert the transaction if it fails
	}

	// Add the gas used by handleRecountVotes
	gasUsed += recountGasUsed

	return nil, gasUsed, nil
}

// handleUndelegate undelegates stake from a validator
func handleUndelegate(evm *vm.EVM, caller common.Address, args []interface{}) ([]byte, uint64, error) {
	// Initialize gas used
	var gasUsed uint64 = 0
	// Get the arguments
	if len(args) != 3 {
		return nil, 0, vm.ErrExecutionReverted
	}
	toValidatorID, ok := args[0].(*big.Int)
	if !ok {
		return nil, 0, vm.ErrExecutionReverted
	}
	wrID, ok := args[1].(*big.Int)
	if !ok {
		return nil, 0, vm.ErrExecutionReverted
	}
	amount, ok := args[2].(*big.Int)
	if !ok {
		return nil, 0, vm.ErrExecutionReverted
	}

	// Check that the validator exists
	revertData, checkGasUsed, err := checkValidatorExists(evm, toValidatorID, "undelegate")
	gasUsed += checkGasUsed
	if err != nil {
		return revertData, gasUsed, err
	}

	// Check that the delegation exists
	revertData, checkGasUsed, err = checkDelegationExists(evm, caller, toValidatorID, "undelegate")
	gasUsed += checkGasUsed
	if err != nil {
		return revertData, gasUsed, err
	}

	// Stash rewards
	// Create arguments for handle_stashRewards
	stashRewardsArgs := []interface{}{caller, toValidatorID}
	// Call handle_stashRewards
	result, stashGasUsed, err := handle_stashRewards(evm, stashRewardsArgs)
	if err != nil {
		return result, gasUsed + stashGasUsed, err
	}

	// Add the gas used by handle_stashRewards
	gasUsed += stashGasUsed

	// Check that the amount is greater than 0
	if amount.Cmp(big.NewInt(0)) <= 0 {
		revertData, err := encodeRevertReason("undelegate", "zero amount")
		if err != nil {
			return nil, 0, vm.ErrExecutionReverted
		}
		return revertData, 0, vm.ErrExecutionReverted
	}

	// Check that the amount is less than or equal to the unlocked stake
	// Create arguments for handleGetUnlockedStake
	getUnlockedStakeArgs := []interface{}{caller, toValidatorID}
	// Call handleGetUnlockedStake
	unlockedStakeResult, unlockGasUsed, err := handleGetUnlockedStake(evm, getUnlockedStakeArgs)
	if err != nil {
		return unlockedStakeResult, gasUsed + unlockGasUsed, err
	}

	// Add the gas used by handleGetUnlockedStake
	gasUsed += unlockGasUsed

	// Unpack the result
	unlockedStakeValues, err := SfcAbi.Methods["getUnlockedStake"].Outputs.Unpack(unlockedStakeResult)
	if err != nil {
		return nil, 0, vm.ErrExecutionReverted
	}

	// The result should be a single *big.Int value
	if len(unlockedStakeValues) != 1 {
		return nil, 0, vm.ErrExecutionReverted
	}

	unlockedStake, ok := unlockedStakeValues[0].(*big.Int)
	if !ok {
		return nil, 0, vm.ErrExecutionReverted
	}
	if amount.Cmp(unlockedStake) > 0 {
		revertData, err := encodeRevertReason("undelegate", "not enough unlocked stake")
		if err != nil {
			return nil, 0, vm.ErrExecutionReverted
		}
		return revertData, 0, vm.ErrExecutionReverted
	}

	// Check that the delegator is allowed to withdraw
	allowed, err := handleCheckAllowedToWithdraw(evm, caller, toValidatorID)
	if err != nil {
		// This is a direct call, not through a handler, so we don't have a revert reason
		return nil, gasUsed, err
	}
	if !allowed {
		revertData, err := encodeRevertReason("undelegate", "outstanding sU2U balance")
		if err != nil {
			return nil, 0, vm.ErrExecutionReverted
		}
		return revertData, 0, vm.ErrExecutionReverted
	}

	// Check that the withdrawal request ID doesn't already exist
	withdrawalRequestSlot, _ := getWithdrawalRequestSlot(caller, toValidatorID, wrID)
	withdrawalRequest := evm.SfcStateDB.GetState(ContractAddress, common.BigToHash(withdrawalRequestSlot))
	withdrawalRequestAmount := new(big.Int).SetBytes(withdrawalRequest.Bytes())
	if withdrawalRequestAmount.Cmp(big.NewInt(0)) != 0 {
		revertData, err := encodeRevertReason("undelegate", "wrID already exists")
		if err != nil {
			return nil, 0, vm.ErrExecutionReverted
		}
		return revertData, 0, vm.ErrExecutionReverted
	}

	// Raw undelegate
	result, rawGasUsed, err := handleRawUndelegate(evm, caller, toValidatorID, amount, true)
	if err != nil {
		return result, gasUsed + rawGasUsed, err
	}

	// Add the gas used by handleRawUndelegate
	gasUsed += rawGasUsed

	// Set the withdrawal request
	withdrawalRequestAmountSlot, slotGasUsed := getWithdrawalRequestAmountSlot(caller, toValidatorID, wrID)
	gasUsed += slotGasUsed
	evm.SfcStateDB.SetState(ContractAddress, common.BigToHash(withdrawalRequestAmountSlot), common.BigToHash(amount))

	withdrawalRequestEpochSlot, epochSlotGasUsed := getWithdrawalRequestEpochSlot(caller, toValidatorID, wrID)
	gasUsed += epochSlotGasUsed
	currentEpochBigInt, _, err := getCurrentEpoch(evm)
	if err != nil {
		return nil, 0, err
	}
	evm.SfcStateDB.SetState(ContractAddress, common.BigToHash(withdrawalRequestEpochSlot), common.BigToHash(currentEpochBigInt))

	withdrawalRequestTimeSlot, timeSlotGasUsed := getWithdrawalRequestTimeSlot(caller, toValidatorID, wrID)
	gasUsed += timeSlotGasUsed
	evm.SfcStateDB.SetState(ContractAddress, common.BigToHash(withdrawalRequestTimeSlot), common.BigToHash(evm.Context.Time))

	// Sync validator
	result, syncGasUsed, err := handleSyncValidator(evm, toValidatorID)
	if err != nil {
		return result, gasUsed + syncGasUsed, err
	}

	// Add the gas used by handleSyncValidator
	gasUsed += syncGasUsed

	// Emit Undelegated event
	topics := []common.Hash{
		SfcAbi.Events["Undelegated"].ID,
		common.BytesToHash(common.LeftPadBytes(caller.Bytes(), 32)), // indexed parameter (delegator)
		common.BigToHash(toValidatorID),                             // indexed parameter (toValidatorID)
		common.BigToHash(wrID),                                      // indexed parameter (wrID)
	}
	data, err := SfcAbi.Events["Undelegated"].Inputs.NonIndexed().Pack(
		amount,
	)
	if err != nil {
		return nil, 0, vm.ErrExecutionReverted
	}
	evm.SfcStateDB.AddLog(&types.Log{
		Address: ContractAddress,
		Topics:  topics,
		Data:    data,
	})

	// Recount votes
	// Get the validator auth address
	validatorAuthSlot, _ := getValidatorAuthSlot(toValidatorID)
	validatorAuth := evm.SfcStateDB.GetState(ContractAddress, common.BigToHash(validatorAuthSlot))
	validatorAuthAddr := common.BytesToAddress(validatorAuth.Bytes())

	// Call handleRecountVotes with strict=true
	result, recountGasUsed, err := handleRecountVotes(evm, caller, validatorAuthAddr, true)
	if err != nil {
		return result, gasUsed + recountGasUsed, err
	}

	// Add the gas used by handleRecountVotes
	gasUsed += recountGasUsed

	return nil, gasUsed, nil
}

// handleInternalDelegate implements the internal _delegate function logic
func handleInternalDelegate(evm *vm.EVM, delegator common.Address, toValidatorID *big.Int, amount *big.Int) ([]byte, uint64, error) {
	// Initialize gas used
	var gasUsed uint64 = 0
	// This function can either handle the delegation logic directly or call the SFCLib contract
	// For this implementation, we'll handle it directly, but we could also call the SFCLib contract
	// return callSFCLibDelegate(evm, delegator, toValidatorID, amount)

	// Check that the validator exists
	revertData, checkGasUsed, err := checkValidatorExists(evm, toValidatorID, "_delegate")
	gasUsed += checkGasUsed
	if err != nil {
		return revertData, gasUsed, err
	}

	// Check that the validator is active
	revertData, checkGasUsed, err = checkValidatorActive(evm, toValidatorID, "_delegate")
	gasUsed += checkGasUsed
	if err != nil {
		return revertData, gasUsed, err
	}

	// Check that the amount is greater than 0
	if amount.Cmp(big.NewInt(0)) <= 0 {
		revertData, err := encodeRevertReason("_delegate", "zero amount")
		if err != nil {
			return nil, 0, vm.ErrExecutionReverted
		}
		return revertData, 0, vm.ErrExecutionReverted
	}

	// Stash rewards
	// Create arguments for handle_stashRewards
	stashRewardsArgs := []interface{}{delegator, toValidatorID}
	// Call handle_stashRewards
	result, stashGasUsed, err := handle_stashRewards(evm, stashRewardsArgs)
	if err != nil {
		return result, gasUsed + stashGasUsed, err
	}

	// Add the gas used by handle_stashRewards
	gasUsed += stashGasUsed

	// Use batch operations for efficient state updates
	batch := NewStateBatch()
	defer batch.Clear()

	// Update the stake using batch operations
	stakeSlot, slotGasUsed := getStakeSlot(delegator, toValidatorID)
	gasUsed += slotGasUsed

	// Read current stake (uses cache if available)
	currentStake := batch.GetState(evm, common.BigToHash(stakeSlot))
	stakeBigInt := GetLargeBigInt().SetBytes(currentStake.Bytes())
	newStake := GetLargeBigInt().Add(stakeBigInt, amount)

	// Queue stake update
	batch.SetState(common.BigToHash(stakeSlot), common.BigToHash(newStake))

	// Update the validator's received stake
	validatorReceivedStakeSlot, slotGasUsed := getValidatorReceivedStakeSlot(toValidatorID)
	gasUsed += slotGasUsed

	// Read current validator received stake
	currentReceivedStake := batch.GetState(evm, common.BigToHash(validatorReceivedStakeSlot))
	receivedStakeBigInt := GetLargeBigInt().SetBytes(currentReceivedStake.Bytes())
	origStake := GetLargeBigInt().Set(receivedStakeBigInt) // Save original stake for _syncValidator
	newReceivedStake := GetLargeBigInt().Add(receivedStakeBigInt, amount)

	// Queue validator stake update
	batch.SetState(common.BigToHash(validatorReceivedStakeSlot), common.BigToHash(newReceivedStake))

	// Update the total stake
	totalStakeSlotHash := common.BigToHash(GetSmallBigInt().SetInt64(totalStakeSlot))
	currentTotalStake := batch.GetState(evm, totalStakeSlotHash)
	totalStakeBigInt := GetLargeBigInt().SetBytes(currentTotalStake.Bytes())
	newTotalStake := GetLargeBigInt().Add(totalStakeBigInt, amount)

	// Queue total stake update
	batch.SetState(totalStakeSlotHash, common.BigToHash(newTotalStake))

	// Update the total active stake if the validator is active
	validatorStatusSlot, slotGasUsed := getValidatorStatusSlot(toValidatorID)
	gasUsed += slotGasUsed

	// Read validator status to check if active
	validatorStatus := batch.GetState(evm, common.BigToHash(validatorStatusSlot))
	validatorStatusBigInt := GetSmallBigInt().SetBytes(validatorStatus.Bytes())

	if validatorStatusBigInt.Cmp(GetSmallBigInt().SetInt64(0)) == 0 { // OK_STATUS
		totalActiveStakeSlotHash := common.BigToHash(GetSmallBigInt().SetInt64(totalActiveStakeSlot))
		currentTotalActiveStake := batch.GetState(evm, totalActiveStakeSlotHash)
		totalActiveStakeBigInt := GetLargeBigInt().SetBytes(currentTotalActiveStake.Bytes())
		newTotalActiveStake := GetLargeBigInt().Add(totalActiveStakeBigInt, amount)

		// Queue total active stake update
		batch.SetState(totalActiveStakeSlotHash, common.BigToHash(newTotalActiveStake))

		// Return temporary big.Int to pool
		PutLargeBigInt(totalActiveStakeBigInt)
		PutLargeBigInt(newTotalActiveStake)
	}

	// Execute all batched state updates
	batchGasUsed := batch.BatchWrite(evm)
	gasUsed += batchGasUsed

	// Return big.Int values to pools
	PutLargeBigInt(stakeBigInt)
	PutLargeBigInt(newStake)
	PutLargeBigInt(receivedStakeBigInt)
	PutLargeBigInt(newReceivedStake)
	PutLargeBigInt(totalStakeBigInt)
	PutLargeBigInt(newTotalStake)
	PutSmallBigInt(validatorStatusBigInt)

	// Sync validator (equivalent to _syncValidator(toValidatorID, origStake == 0))
	isZeroOrig := origStake.Cmp(big.NewInt(0)) == 0
	_, syncGasUsed, err := handleSyncValidator(evm, toValidatorID, isZeroOrig)
	if err != nil {
		return nil, gasUsed, err
	}
	gasUsed += syncGasUsed

	// Emit Delegated event
	topics := []common.Hash{
		SfcAbi.Events["Delegated"].ID,
		common.BytesToHash(common.LeftPadBytes(delegator.Bytes(), 32)), // indexed parameter (delegator)
		common.BigToHash(toValidatorID),                                // indexed parameter (toValidatorID)
	}

	// Pack the non-indexed parameters
	// The event definition is:
	// event Delegated(address indexed delegator, uint256 indexed toValidatorID, uint256 amount)
	data := common.BigToHash(amount).Bytes() // amount

	evm.SfcStateDB.AddLog(&types.Log{
		Address: ContractAddress,
		Topics:  topics,
		Data:    data,
	})

	// Get the validator auth address
	validatorAuthSlot, slotGasUsed := getValidatorAuthSlot(toValidatorID)
	gasUsed += slotGasUsed
	validatorAuth := evm.SfcStateDB.GetState(ContractAddress, common.BigToHash(validatorAuthSlot))
	gasUsed += SloadGasCost
	validatorAuthAddr := common.BytesToAddress(validatorAuth.Bytes())

	// Recount votes
	_, recountGasUsed, err := handleRecountVotes(evm, delegator, validatorAuthAddr, true)
	if err != nil {
		return nil, gasUsed, err
	}
	gasUsed += recountGasUsed

	// Check delegated stake limit
	limitExceeded, checkGasUsed, err := checkDelegatedStakeLimit(evm, toValidatorID)
	gasUsed += checkGasUsed
	if err != nil {
		return nil, gasUsed, err
	}
	if limitExceeded {
		revertData, err := encodeRevertReason("_delegate", "validator's delegations limit is exceeded")
		if err != nil {
			return nil, gasUsed, vm.ErrExecutionReverted
		}
		return revertData, gasUsed, vm.ErrExecutionReverted
	}

	return nil, gasUsed, nil
}

// handleRawUndelegate implements the _rawUndelegate function logic
func handleRawUndelegate(evm *vm.EVM, delegator common.Address, toValidatorID *big.Int, amount *big.Int, strict bool) ([]byte, uint64, error) {
	// Initialize gas used
	var gasUsed uint64 = 0
	// Use batch operations for efficient state updates
	batch := NewStateBatch()
	defer batch.Clear()

	// Update the stake
	stakeSlot, slotGasUsed := getStakeSlot(delegator, toValidatorID)
	gasUsed += slotGasUsed

	// Read current stake using batch GetState
	currentStake := batch.GetState(evm, common.BigToHash(stakeSlot))
	stakeBigInt := GetLargeBigInt().SetBytes(currentStake.Bytes())
	newStake := GetLargeBigInt().Sub(stakeBigInt, amount)

	// Queue stake update
	batch.SetState(common.BigToHash(stakeSlot), common.BigToHash(newStake))

	// Update the validator's received stake
	validatorReceivedStakeSlot, slotGasUsed := getValidatorReceivedStakeSlot(toValidatorID)
	gasUsed += slotGasUsed

	// Read current validator received stake
	currentReceivedStake := batch.GetState(evm, common.BigToHash(validatorReceivedStakeSlot))
	receivedStakeBigInt := GetLargeBigInt().SetBytes(currentReceivedStake.Bytes())
	newReceivedStake := GetLargeBigInt().Sub(receivedStakeBigInt, amount)

	// Queue validator stake update
	batch.SetState(common.BigToHash(validatorReceivedStakeSlot), common.BigToHash(newReceivedStake))

	// Update the total stake
	totalStakeSlotHash := common.BigToHash(GetSmallBigInt().SetInt64(totalStakeSlot))
	currentTotalStake := batch.GetState(evm, totalStakeSlotHash)
	totalStakeBigInt := GetLargeBigInt().SetBytes(currentTotalStake.Bytes())
	newTotalStake := GetLargeBigInt().Sub(totalStakeBigInt, amount)

	// Queue total stake update
	batch.SetState(totalStakeSlotHash, common.BigToHash(newTotalStake))

	// Update the total active stake if the validator is active
	validatorStatusSlot, _ := getValidatorStatusSlot(toValidatorID)
	validatorStatus := batch.GetState(evm, common.BigToHash(validatorStatusSlot))
	validatorStatusBigInt := GetSmallBigInt().SetBytes(validatorStatus.Bytes())

	if validatorStatusBigInt.Cmp(GetSmallBigInt().SetInt64(0)) == 0 { // OK_STATUS
		totalActiveStakeSlotHash := common.BigToHash(GetSmallBigInt().SetInt64(totalActiveStakeSlot))
		currentTotalActiveStake := batch.GetState(evm, totalActiveStakeSlotHash)
		totalActiveStakeBigInt := GetLargeBigInt().SetBytes(currentTotalActiveStake.Bytes())
		newTotalActiveStake := GetLargeBigInt().Sub(totalActiveStakeBigInt, amount)

		// Queue total active stake update
		batch.SetState(totalActiveStakeSlotHash, common.BigToHash(newTotalActiveStake))

		// Return temporary big.Int to pool
		PutLargeBigInt(totalActiveStakeBigInt)
		PutLargeBigInt(newTotalActiveStake)
	}

	// Execute all batched state updates
	batchGasUsed := batch.BatchWrite(evm)
	gasUsed += batchGasUsed

	// Return big.Int values to pools
	PutLargeBigInt(stakeBigInt)
	PutLargeBigInt(newStake)
	PutLargeBigInt(receivedStakeBigInt)
	PutLargeBigInt(newReceivedStake)
	PutLargeBigInt(totalStakeBigInt)
	PutLargeBigInt(newTotalStake)
	PutSmallBigInt(validatorStatusBigInt)

	// Get the self-stake
	// Create arguments for handleGetSelfStake
	args := []interface{}{toValidatorID}
	// Call handleGetSelfStake
	result, selfStakeGasUsed, err := handleGetSelfStake(evm, args)
	if err != nil {
		return nil, gasUsed + selfStakeGasUsed, err
	}

	// Add the gas used by handleGetSelfStake
	gasUsed += selfStakeGasUsed

	// Unpack the result
	selfStakeValues, err := SfcAbi.Methods["getSelfStake"].Outputs.Unpack(result)
	if err != nil {
		return nil, gasUsed, vm.ErrExecutionReverted
	}

	// The result should be a single *big.Int value
	if len(selfStakeValues) != 1 {
		return nil, gasUsed, vm.ErrExecutionReverted
	}

	selfStake, ok := selfStakeValues[0].(*big.Int)
	if !ok {
		return nil, gasUsed, vm.ErrExecutionReverted
	}

	// Check if the validator should be deactivated
	if selfStake.Cmp(big.NewInt(0)) == 0 {
		// Set the validator as deactivated
		evm.SfcStateDB.SetState(ContractAddress, common.BigToHash(validatorStatusSlot), common.BigToHash(big.NewInt(1))) // WITHDRAWN_BIT
	} else if validatorStatusBigInt.Cmp(big.NewInt(0)) == 0 { // OK_STATUS
		// Check that the self-stake is at least the minimum self-stake
		minSelfStakeBigInt := getConstantsManagerVariable("minSelfStake")
		if selfStake.Cmp(minSelfStakeBigInt) < 0 {
			// Set the validator as deactivated
			evm.SfcStateDB.SetState(ContractAddress, common.BigToHash(validatorStatusSlot), common.BigToHash(big.NewInt(1))) // WITHDRAWN_BIT
		} else {
			// Check that the delegated stake is within the limit
			withinLimit, err := handleCheckDelegatedStakeLimit(evm, toValidatorID)
			if err != nil {
				return nil, gasUsed, err
			}
			if !withinLimit {
				// Set the validator as deactivated
				evm.SfcStateDB.SetState(ContractAddress, common.BigToHash(validatorStatusSlot), common.BigToHash(big.NewInt(1))) // WITHDRAWN_BIT
			}
		}
	}

	// Get the validator auth address
	validatorAuthSlot, slotGasUsed := getValidatorAuthSlot(toValidatorID)
	gasUsed += slotGasUsed
	validatorAuth := evm.SfcStateDB.GetState(ContractAddress, common.BigToHash(validatorAuthSlot))
	gasUsed += params.ColdSloadCostEIP2929 // Add gas for SLOAD
	validatorAuthAddr := common.BytesToAddress(validatorAuth.Bytes())

	// Recount votes
	_, recountGasUsed, err := handleRecountVotes(evm, delegator, validatorAuthAddr, strict)
	if err != nil && strict {
		return nil, gasUsed + recountGasUsed, err
	}

	// Add the gas used by handleRecountVotes
	gasUsed += recountGasUsed

	return nil, gasUsed, nil
}
